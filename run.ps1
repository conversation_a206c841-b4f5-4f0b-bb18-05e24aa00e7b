Write-Host "Employee Management System - <PERSON>" -ForegroundColor Green
Write-Host "Diwaniyah Governorate / Payroll Department" -ForegroundColor Cyan
Write-Host "Email: <EMAIL> | Phone: 07727232639" -ForegroundColor Yellow
Write-Host ""

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js not found!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check project files
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found!" -ForegroundColor Red
    Write-Host "Make sure you are in the correct directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Project files found" -ForegroundColor Green

# Create database folder
if (-not (Test-Path "database_safe")) {
    New-Item -ItemType Directory -Name "database_safe" | Out-Null
    Write-Host "Database folder created" -ForegroundColor Green
}

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to install dependencies!" -ForegroundColor Red
        Write-Host "Check your internet connection" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Starting the application..." -ForegroundColor Green
Write-Host "The program window will open shortly" -ForegroundColor Cyan
Write-Host "Do not close this window while the program is running" -ForegroundColor Yellow
Write-Host ""

# Start the application
npm start

Write-Host ""
Write-Host "Program closed" -ForegroundColor Yellow
Write-Host "Thank you for using Employee Management System!" -ForegroundColor Green
Read-Host "Press Enter to exit"
