@echo off
chcp 65001 >nul

REM تعيين أيقونة مخصصة للنافذة
title 🚀 نظام إدارة بيانات الموظفين - المبرمج: علي عاجل خشّان المحنّة

REM تعيين ألوان جميلة للنافذة
color 0B

REM إخفاء المؤشر
echo [?25l

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              🚀 جاري تشغيل البرنامج... 🚀
echo.
echo                         ⏳ يرجى الانتظار حتى فتح نافذة البرنامج ⏳
echo.

REM شريط التحميل المتحرك
echo                              [                    ] 0%%
timeout /t 1 /nobreak >nul
cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              🚀 جاري تشغيل البرنامج... 🚀
echo.
echo                         ⏳ يرجى الانتظار حتى فتح نافذة البرنامج ⏳
echo.
echo                              [████                ] 20%%
timeout /t 1 /nobreak >nul

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    cls
    color 0C
    echo.
    echo                    ████████████████████████████████████████████████████████████████████
    echo                    ██                                                                ██
    echo                    ██                        ❌ خطأ ❌                              ██
    echo                    ██                                                                ██
    echo                    ██              Node.js غير مثبت على النظام                     ██
    echo                    ██                                                                ██
    echo                    ██           📥 يرجى تثبيت Node.js من الرابط:                   ██
    echo                    ██                  https://nodejs.org                           ██
    echo                    ██                                                                ██
    echo                    ██                    📞 للدعم الفني:                           ██
    echo                    ██              📧 <EMAIL>                           ██
    echo                    ██              📱 07727232639                                   ██
    echo                    ██                                                                ██
    echo                    ████████████████████████████████████████████████████████████████████
    echo.
    echo                         اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              🚀 جاري تشغيل البرنامج... 🚀
echo.
echo                         ⏳ يرجى الانتظار حتى فتح نافذة البرنامج ⏳
echo.
echo                              [████████        ] 40%%

REM التحقق من وجود ملفات المشروع
if not exist "package.json" (
    cls
    color 0C
    echo.
    echo                    ████████████████████████████████████████████████████████████████████
    echo                    ██                                                                ██
    echo                    ██                        ❌ خطأ ❌                              ██
    echo                    ██                                                                ██
    echo                    ██                ملفات المشروع غير موجودة                      ██
    echo                    ██                                                                ██
    echo                    ██         📁 تأكد من وجود جميع ملفات البرنامج                  ██
    echo                    ██                   في نفس المجلد                              ██
    echo                    ██                                                                ██
    echo                    ████████████████████████████████████████████████████████████████████
    echo.
    echo                         اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" (
    mkdir database_safe
)

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              🚀 جاري تشغيل البرنامج... 🚀
echo.
echo                         ⏳ يرجى الانتظار حتى فتح نافذة البرنامج ⏳
echo.
echo                              [████████████    ] 60%%

REM التحقق من تثبيت التبعيات
if not exist "node_modules" (
    cls
    echo.
    echo                    ████████████████████████████████████████████████████████████████████
    echo                    ██                                                                ██
    echo                    ██                   📦 تثبيت التبعيات 📦                       ██
    echo                    ██                                                                ██
    echo                    ██              جاري تثبيت التبعيات للمرة الأولى...              ██
    echo                    ██                                                                ██
    echo                    ██                ⏳ قد يستغرق بضع دقائق ⏳                      ██
    echo                    ██                                                                ██
    echo                    ████████████████████████████████████████████████████████████████████
    echo.
    
    npm install --silent
    if %ERRORLEVEL% NEQ 0 (
        cls
        color 0C
        echo.
        echo                    ████████████████████████████████████████████████████████████████████
        echo                    ██                                                                ██
        echo                    ██                        ❌ خطأ ❌                              ██
        echo                    ██                                                                ██
        echo                    ██                   خطأ في تثبيت التبعيات                      ██
        echo                    ██                                                                ██
        echo                    ██            🌐 تأكد من اتصالك بالإنترنت                      ██
        echo                    ██                   وحاول مرة أخرى                            ██
        echo                    ██                                                                ██
        echo                    ████████████████████████████████████████████████████████████████████
        echo.
        echo                         اضغط أي زر للخروج...
        pause >nul
        exit /b 1
    )
)

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              🚀 جاري تشغيل البرنامج... 🚀
echo.
echo                         ⏳ يرجى الانتظار حتى فتح نافذة البرنامج ⏳
echo.
echo                              [████████████████] 80%%

timeout /t 1 /nobreak >nul

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ██                                                                ██
echo                    ██           ⭐ نظام إدارة بيانات الموظفين ⭐                    ██
echo                    ██                                                                ██
echo                    ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻               ██
echo                    ██                                                                ██
echo                    ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️               ██
echo                    ██                                                                ██
echo                    ██  📧 <EMAIL>  📱 07727232639                      ██
echo                    ██                                                                ██
echo                    ██  🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢  ██
echo                    ████████████████████████████████████████████████████████████████████
echo.
echo.
echo                              ✨ تشغيل البرنامج... ✨
echo.
echo                         🎉 ستفتح نافذة البرنامج خلال ثوانٍ قليلة 🎉
echo.
echo                              [████████████████████] 100%%

REM تشغيل البرنامج
start "" npm start

REM انتظار قليل ثم إغلاق هذه النافذة
timeout /t 5 /nobreak >nul

cls
echo.
echo                    ████████████████████████████████████████████████████████████████████
echo                    ██                                                                ██
echo                    ██                      ✅ تم التشغيل بنجاح ✅                     ██
echo                    ██                                                                ██
echo                    ██                 🎉 البرنامج يعمل الآن! 🎉                      ██
echo                    ██                                                                ██
echo                    ██              يمكنك إغلاق هذه النافذة بأمان                     ██
echo                    ██                                                                ██
echo                    ██                    شكراً لاستخدام البرنامج                     ██
echo                    ██                                                                ██
echo                    ████████████████████████████████████████████████████████████████████
echo.

timeout /t 3 /nobreak >nul
exit
