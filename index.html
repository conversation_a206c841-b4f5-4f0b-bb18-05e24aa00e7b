<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة بيانات الموظفين</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة البداية -->
    <div id="splashScreen" class="splash-screen">
        <div class="splash-content">
            <div class="app-title">
                <h1 id="appTitle" class="animated-title"></h1>
            </div>
            <div class="programmer-info">
                <h2 id="programmerName" class="programmer-name"></h2>
            </div>
            <div class="organization-info">
                <h3 id="organizationName" class="organization-text"></h3>
            </div>
            <div class="programmer-name-display">
                <h1 id="programmerNameDisplay" class="programmer-name-styled"></h1>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- الواجهة الرئيسية -->
    <div id="mainApp" class="main-app hidden">
        <!-- شريط علوي مع اسم المبرمج -->
        <header class="app-header">
            <div class="programmer-signature">
                <span class="glowing-text">المبرمج: علي عاجل خشّان المحنّة</span>
            </div>
            <div class="app-logo">
                <i class="fas fa-building"></i>
                <span>نظام إدارة بيانات الموظفين</span>
            </div>
        </header>

        <!-- الواجهة الرئيسية لاختيار السنة -->
        <div id="yearSelection" class="year-selection-container">
            <div class="year-selection-card">
                <h2 class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    السجل السنوي
                </h2>
                <div class="year-input-group">
                    <label for="yearSelect">اختر السنة:</label>
                    <div class="input-with-dropdown">
                        <input type="number" id="yearInput" placeholder="أدخل السنة (مثل: 2025)" min="2000" max="2100">
                        <button id="dropdownBtn" class="dropdown-btn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div id="yearDropdown" class="year-dropdown hidden">
                            <div class="dropdown-header">السنوات المحفوظة:</div>
                            <div id="yearsList" class="years-list"></div>
                        </div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button id="addYearBtn" class="btn btn-secondary">
                        <i class="fas fa-plus"></i>
                        إضافة سنة جديدة
                    </button>
                    <button id="enterYearBtn" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        دخول
                    </button>
                </div>
            </div>
        </div>

        <!-- واجهة التبويبات -->
        <div id="tabsInterface" class="tabs-interface hidden">
            <!-- شريط التبويبات -->
            <nav class="tabs-nav">
                <button class="tab-btn active" data-tab="employee-info">
                    <i class="fas fa-user"></i>
                    معلومات الموظف
                </button>
                <button class="tab-btn" data-tab="reports">
                    <i class="fas fa-chart-bar"></i>
                    التقرير
                </button>
                <button class="tab-btn" data-tab="comparison" id="comparisonTab">
                    <i class="fas fa-balance-scale"></i>
                    المقارنة السنوية
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="btn btn-exit" onclick="exitApplication()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
                <button id="backToMainBtn" class="back-btn">
                    <i class="fas fa-home"></i>
                    العودة للقائمة الرئيسية
                </button>
            </nav>

            <!-- محتوى التبويبات -->
            <div class="tabs-content">
                <!-- تبويب معلومات الموظف -->
                <div id="employee-info" class="tab-content active">
                    <div class="content-header">
                        <h2><i class="fas fa-user-plus"></i> إضافة معلومات موظف جديد</h2>
                        <div class="current-year">السنة: <span id="currentYear"></span></div>
                    </div>
                    
                    <form id="employeeForm" class="employee-form">
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> البيانات الأساسية</h3>
                            <div class="form-group">
                                <label for="employeeName">اسم الموظف:</label>
                                <input type="text" id="employeeName" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="spouseName">اسم الزوج/الزوجة:</label>
                                <input type="text" id="spouseName">
                            </div>
                            
                            <div class="form-group spouse-status">
                                <label>حالة الزوج/الزوجة:</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="spouseEmployee" name="spouseStatus" value="موظف">
                                        <span class="checkmark"></span>
                                        موظف
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="spouseNotEmployee" name="spouseStatus" value="غير موظف">
                                        <span class="checkmark"></span>
                                        غير موظف
                                    </label>
                                </div>
                                <input type="text" id="spouseOther" placeholder="أخرى (متوفى - متقاعد)" class="other-input">
                            </div>
                            
                            <div class="form-group" id="benefitGroup" style="display: none;">
                                <label for="benefitStatus">عدم استفادة:</label>
                                <select id="benefitStatus">
                                    <option value="">اختر...</option>
                                    <option value="زودنا">زودنا</option>
                                    <option value="لم يزودنا">لم يزودنا</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-child"></i> معلومات الأطفال</h3>
                            <div class="form-group">
                                <label for="childrenCount">عدد الأبناء:</label>
                                <select id="childrenCount">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9</option>
                                    <option value="10">10</option>
                                </select>
                            </div>
                            
                            <div id="childrenDetails" class="children-details"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i>
                                حفظ البيانات
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>

                <!-- تبويب التقرير -->
                <div id="reports" class="tab-content">
                    <div class="content-header">
                        <h2><i class="fas fa-chart-bar"></i> التقرير السنوي</h2>
                        <div class="current-year">السنة: <span class="year-display"></span></div>
                    </div>
                    
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-users"></i></div>
                            <div class="stat-info">
                                <h3>العدد الكلي للموظفين</h3>
                                <span id="totalEmployees" class="stat-number">0</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                            <div class="stat-info">
                                <h3>عدد الزوجية الكاملة</h3>
                                <span id="completeRecords" class="stat-number">0</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                            <div class="stat-info">
                                <h3>عدد الموظفين لديهم نقوصات</h3>
                                <span id="incompleteRecords" class="stat-number">0</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-user-times"></i></div>
                            <div class="stat-info">
                                <h3>الغير مزودين</h3>
                                <span id="notProvided" class="stat-number">0</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="report-actions">
                        <button id="filterDataBtn" class="btn btn-primary">
                            <i class="fas fa-filter"></i>
                            التصفية
                        </button>
                        <button id="exportExcelBtn" class="btn btn-success">
                            <i class="fas fa-file-excel"></i>
                            تصدير إلى Excel
                        </button>
                        <button id="printReportBtn" class="btn btn-info">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                    
                    <div id="dataTable" class="data-table-container">
                        <!-- سيتم إدراج الجدول هنا -->
                    </div>
                </div>

                <!-- تبويب المقارنة السنوية -->
                <div id="comparison" class="tab-content">
                    <div class="content-header">
                        <h2><i class="fas fa-balance-scale"></i> المقارنة السنوية</h2>
                        <div class="current-year">السنة: <span class="year-display"></span></div>
                    </div>
                    
                    <div class="comparison-filters">
                        <h3>خيارات التصفية:</h3>
                        <div class="filter-buttons">
                            <button class="filter-btn" data-filter="children-decrease">
                                <i class="fas fa-arrow-down"></i>
                                النقص في الأبناء
                            </button>
                            <button class="filter-btn" data-filter="incomplete">
                                <i class="fas fa-exclamation-circle"></i>
                                الغير كاملة
                            </button>
                            <button class="filter-btn" data-filter="complete">
                                <i class="fas fa-check-circle"></i>
                                الكاملة
                            </button>
                            <button class="filter-btn" data-filter="spouse-employee">
                                <i class="fas fa-user-tie"></i>
                                الزوج/الزوجة الموظف
                            </button>
                        </div>
                    </div>
                    
                    <div id="comparisonTable" class="data-table-container">
                        <!-- سيتم إدراج جدول المقارنة هنا -->
                    </div>
                    
                    <div class="comparison-actions">
                        <button id="exportComparisonBtn" class="btn btn-success">
                            <i class="fas fa-file-excel"></i>
                            تصدير إلى Excel
                        </button>
                        <button id="printComparisonBtn" class="btn btn-info">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>

                <!-- تبويب الإعدادات -->
                <div id="settings" class="tab-content">
                    <div class="content-header">
                        <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                    </div>
                    
                    <div class="settings-sections">
                        <div class="settings-section">
                            <h3><i class="fas fa-palette"></i> ثيمات البرنامج</h3>
                            <div class="themes-grid">
                                <button class="theme-btn active" data-theme="volcano">
                                    <div class="theme-preview volcano-preview"></div>
                                    <span>البركاني</span>
                                </button>
                                <button class="theme-btn" data-theme="violet-blue">
                                    <div class="theme-preview violet-blue-preview"></div>
                                    <span>البنفسجي والأزرق</span>
                                </button>
                                <button class="theme-btn" data-theme="silver-yellow">
                                    <div class="theme-preview silver-yellow-preview"></div>
                                    <span>الفضي والأصفر</span>
                                </button>
                                <button class="theme-btn" data-theme="sky-blue">
                                    <div class="theme-preview sky-blue-preview"></div>
                                    <span>السمائي</span>
                                </button>
                                <button class="theme-btn" data-theme="grey-cyan">
                                    <div class="theme-preview grey-cyan-preview"></div>
                                    <span>الرصاصي والسماوي</span>
                                </button>
                                <button class="theme-btn" data-theme="grass-green">
                                    <div class="theme-preview grass-green-preview"></div>
                                    <span>الأخضر الحشيشي</span>
                                </button>
                                <button class="theme-btn" data-theme="orange">
                                    <div class="theme-preview orange-preview"></div>
                                    <span>البرتقالي</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3><i class="fas fa-database"></i> إدارة قاعدة البيانات</h3>
                            <div class="database-actions">
                                <button id="exportDataBtn" class="btn btn-warning">
                                    <i class="fas fa-download"></i>
                                    تصدير البيانات
                                </button>
                                <button id="clearDataBtn" class="btn btn-danger">
                                    <i class="fas fa-trash"></i>
                                    تفريغ القاعدة
                                </button>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3><i class="fas fa-user-circle"></i> نبذة عن المبرمج وحقوق النشر</h3>
                            <div class="programmer-bio">
                                <div class="bio-content">
                                    <h4 class="programmer-title">المبرمج: علي عاجل خشّان المحنّة</h4>
                                    <p class="bio-text">
                                        مطور برمجيات متخصص في تصميم وتطوير التطبيقات العصرية والتفاعلية. 
                                        يتميز بخبرة واسعة في تطوير أنظمة إدارة البيانات والواجهات المستخدم الحديثة.
                                    </p>
                                    <div class="achievements">
                                        <h5>الأعمال البرمجية:</h5>
                                        <ul>
                                            <li>تطوير أنظمة إدارة الموظفين والموارد البشرية</li>
                                            <li>تصميم واجهات مستخدم عصرية وتفاعلية</li>
                                            <li>برمجة تطبيقات سطح المكتب والويب</li>
                                            <li>تطوير أنظمة قواعد البيانات المتقدمة</li>
                                        </ul>
                                    </div>
                                    <div class="copyright">
                                        <p><i class="fas fa-copyright"></i> جميع الحقوق محفوظة © 2025 - علي عاجل خشّان المحنّة</p>
                                        <p>هذا البرنامج محمي بحقوق النشر والملكية الفكرية</p>
                                        <div class="contact-info">
                                            <p><i class="fas fa-envelope"></i> البريد الإلكتروني: <EMAIL></p>
                                            <p><i class="fas fa-phone"></i> الهاتف/واتساب: 07727232639</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <h3 id="confirmTitle">تأكيد العملية</h3>
            <p id="confirmMessage">هل أنت متأكد من هذه العملية؟</p>
            <div class="modal-actions">
                <button id="confirmYes" class="btn btn-danger">نعم</button>
                <button id="confirmNo" class="btn btn-secondary">لا</button>
            </div>
        </div>
    </div>

    <!-- نافذة الرسائل -->
    <div id="messageModal" class="modal hidden">
        <div class="modal-content">
            <h3 id="messageTitle">رسالة</h3>
            <p id="messageText"></p>
            <div class="modal-actions">
                <button id="messageOk" class="btn btn-primary">موافق</button>
            </div>
        </div>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/database.js"></script>
    <script src="scripts/themes.js"></script>
    <script src="scripts/animations.js"></script>
</body>
</html>
