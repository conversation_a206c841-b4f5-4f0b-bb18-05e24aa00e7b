# نظام إدارة بيانات الموظفين

## المبرمج: علي عاجل خشّان المحنّة

### وصف البرنامج
نظام شامل لإدارة بيانات الموظفين السنوية مع واجهة عصرية وتفاعلية. يتميز البرنامج بتصميم حديث وألوان جذابة مع إمكانيات متقدمة لإدارة البيانات والتقارير.

### الميزات الرئيسية

#### 🎨 التصميم والواجهة
- شاشة افتتاحية تفاعلية مع حركات جذابة
- 7 ثيمات ملونة مختلفة (البركاني، البنفسجي والأزرق، الفضي والأصفر، السمائي، الرصاصي والسماوي، الأخضر الحشيشي، البرتقالي)
- واجهة مستخدم عصرية ومتجاوبة
- تأثيرات بصرية وحركات سلسة
- اسم المبرمج متوهج في كل صفحة

#### 📊 إدارة البيانات
- إدارة بيانات الموظفين حسب السنة
- معلومات شاملة للموظف والزوج/الزوجة
- إدارة بيانات الأطفال (حتى 4 أطفال بتفاصيل كاملة)
- التحقق التلقائي من أعمار الأطفال والمراحل الدراسية
- نظام حالات الأطفال (مسموح/يرفع الابن)

#### 📈 التقارير والإحصائيات
- إحصائيات شاملة (العدد الكلي، الزوجية الكاملة، النقوصات، الغير مزودين)
- جداول تفاعلية قابلة للفرز والبحث
- تصدير البيانات إلى Excel
- طباعة التقارير
- مقارنة سنوية بين البيانات

#### 🔧 الإعدادات والأدوات
- تغيير الثيمات بسهولة
- تصدير ونسخ احتياطي لقاعدة البيانات
- تفريغ قاعدة البيانات
- نبذة عن المبرمج وحقوق النشر

### متطلبات التشغيل
- نظام التشغيل: Windows 10 أو 11
- لا يتطلب تثبيت أي برامج إضافية
- يعمل كتطبيق مستقل

### كيفية التثبيت والتشغيل

#### الطريقة الأولى: تشغيل مباشر (للتطوير)
```bash
# تثبيت Node.js إذا لم يكن مثبتاً
# تحميل المشروع
cd ZOGEIA

# تثبيت التبعيات
npm install

# تشغيل البرنامج
npm start
```

#### الطريقة الثانية: بناء تطبيق مستقل
```bash
# بناء التطبيق للتوزيع
npm run build

# أو بناء بدون نشر
npm run dist
```

### هيكل المشروع
```
ZOGEIA/
├── main.js                 # الملف الرئيسي لـ Electron
├── index.html             # الواجهة الرئيسية
├── package.json           # إعدادات المشروع
├── styles/                # ملفات التصميم
│   ├── main.css          # التصميم الرئيسي
│   ├── themes.css        # الثيمات
│   └── animations.css    # الحركات
├── scripts/               # ملفات JavaScript
│   ├── main.js           # الوظائف الرئيسية
│   ├── database.js       # إدارة قاعدة البيانات
│   ├── themes.js         # إدارة الثيمات
│   └── animations.js     # إدارة الحركات
├── assets/                # الأيقونات والصور
└── database_safe/         # مجلد قاعدة البيانات
    └── employees.db       # قاعدة البيانات الرئيسية
```

### استخدام البرنامج

#### 1. البداية
- عند تشغيل البرنامج، ستظهر شاشة افتتاحية جذابة
- بعد 5 ثوانٍ، ينتقل تلقائياً للواجهة الرئيسية

#### 2. اختيار السنة
- أدخل السنة المطلوبة (2000-2100)
- أو اختر من السنوات المحفوظة مسبقاً
- اضغط "إضافة سنة جديدة" لحفظ سنة جديدة
- اضغط "دخول" للانتقال لواجهة التبويبات

#### 3. إدخال بيانات الموظفين
- **التبويب الأول**: معلومات الموظف
  - أدخل اسم الموظف
  - أدخل اسم الزوج/الزوجة وحالته
  - حدد عدد الأطفال (0-10)
  - أدخل تفاصيل كل طفل (الاسم، تاريخ الميلاد، المرحلة الدراسية)
  - سيتم التحقق تلقائياً من صحة الأعمار

#### 4. عرض التقارير
- **التبويب الثاني**: التقرير
  - عرض الإحصائيات الشاملة
  - جدول تفاعلي بجميع البيانات
  - إمكانية التصدير والطباعة

#### 5. المقارنة السنوية
- **التبويب الثالث**: المقارنة السنوية (يظهر عند وجود أكثر من سنة)
  - مقارنة البيانات بين السنوات
  - تصفيات متقدمة (النقص في الأطفال، الكاملة، الغير كاملة، إلخ)

#### 6. الإعدادات
- **التبويب الرابع**: الإعدادات
  - تغيير الثيم (7 خيارات)
  - إدارة قاعدة البيانات
  - نبذة عن المبرمج

### الثيمات المتاحة
1. **البركاني**: أحمر داكن وأسود
2. **البنفسجي والأزرق**: تدرجات بنفسجية وزرقاء
3. **الفضي والأصفر**: ألوان فضية وذهبية
4. **السمائي**: تدرجات زرقاء سماوية
5. **الرصاصي والسماوي**: رمادي وسماوي
6. **الأخضر الحشيشي**: تدرجات خضراء طبيعية
7. **البرتقالي**: ألوان برتقالية دافئة

### قواعد التحقق من الأعمار
- **مسموح**: عمر 18 سنة أو أقل
- **يرفع الابن**:
  - أكبر من 18 سنة في متوسطة أو إعدادية
  - 20 سنة أو أكثر في متوسطة أو إعدادية
  - 24 سنة أو أكثر في الجامعة

### النسخ الاحتياطي والأمان
- تخزين آمن في مجلد `database_safe`
- إمكانية إنشاء نسخ احتياطية
- تصدير البيانات لحفظها خارجياً
- تفريغ قاعدة البيانات عند الحاجة

### الدعم الفني
للدعم الفني أو الاستفسارات، يرجى التواصل مع المبرمج:
**علي عاجل خشّان المحنّة**

### حقوق النشر
© 2025 - علي عاجل خشّان المحنّة
جميع الحقوق محفوظة. هذا البرنامج محمي بحقوق النشر والملكية الفكرية.

### إصدارات مستقبلية
- إضافة المزيد من التقارير المتقدمة
- دعم قواعد بيانات خارجية
- واجهة ويب للوصول عن بُعد
- تطبيق موبايل مصاحب
