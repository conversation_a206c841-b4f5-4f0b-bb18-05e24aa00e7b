/* الحركات والتأثيرات البصرية */

/* حركة تدرج الخلفية */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* حركة النص الملون */
@keyframes rainbowText {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 25% 50%;
    }
    50% {
        background-position: 50% 50%;
    }
    75% {
        background-position: 75% 50%;
    }
    100% {
        background-position: 100% 50%;
    }
}

/* حركة النبض */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* حركة التحميل */
@keyframes loading {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

/* حركة الألوان المتوهجة */
@keyframes glowingColors {
    0% {
        background-position: 0% 50%;
        filter: brightness(1) saturate(1);
    }
    25% {
        background-position: 25% 50%;
        filter: brightness(1.2) saturate(1.3);
    }
    50% {
        background-position: 50% 50%;
        filter: brightness(1.4) saturate(1.5);
    }
    75% {
        background-position: 75% 50%;
        filter: brightness(1.2) saturate(1.3);
    }
    100% {
        background-position: 100% 50%;
        filter: brightness(1) saturate(1);
    }
}

/* حركة ظهور النص حرف بحرف */
@keyframes typeWriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

/* حركة سقوط الأحرف */
@keyframes letterDrop {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* حركة الدوران */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* حركة الاهتزاز */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* حركة الارتداد */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -15px, 0);
    }
    70% {
        transform: translate3d(0, -7px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* حركة التلاشي */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* حركة التكبير */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

/* حركة الانزلاق من اليمين */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* حركة الانزلاق من اليسار */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* حركة الانزلاق من الأعلى */
@keyframes slideInTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* حركة الانزلاق من الأسفل */
@keyframes slideInBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تطبيق الحركات على العناصر */
.animated-title {
    animation: fadeIn 2s ease-out, bounce 1s ease-out 2s;
}

.programmer-name {
    animation: slideInBottom 1.5s ease-out 1s both;
}

.animated-icon {
    animation: zoomIn 1s ease-out 2.5s both, pulse 2s infinite 3.5s;
}

.loading-progress {
    animation: loading 3s ease-in-out;
}

/* حركات التفاعل */
.btn:hover {
    animation: pulse 0.3s ease-in-out;
}

.stat-card:hover {
    animation: bounce 0.5s ease-in-out;
}

.tab-btn:hover {
    animation: pulse 0.2s ease-in-out;
}

.theme-btn:hover {
    animation: bounce 0.3s ease-in-out;
}

/* حركات الظهور للعناصر */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

.zoom-in {
    animation: zoomIn 0.3s ease-out;
}

.zoom-out {
    animation: zoomOut 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-top {
    animation: slideInTop 0.5s ease-out;
}

.slide-in-bottom {
    animation: slideInBottom 0.5s ease-out;
}

/* حركات خاصة للنصوص */
.typing-effect {
    overflow: hidden;
    border-left: 3px solid;
    white-space: nowrap;
    animation: typeWriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: currentColor;
    }
}

/* حركة الأحرف المتساقطة */
.letter-drop {
    display: inline-block;
    animation: letterDrop 0.5s ease-out both;
}

.letter-drop:nth-child(1) { animation-delay: 0.1s; }
.letter-drop:nth-child(2) { animation-delay: 0.2s; }
.letter-drop:nth-child(3) { animation-delay: 0.3s; }
.letter-drop:nth-child(4) { animation-delay: 0.4s; }
.letter-drop:nth-child(5) { animation-delay: 0.5s; }
.letter-drop:nth-child(6) { animation-delay: 0.6s; }
.letter-drop:nth-child(7) { animation-delay: 0.7s; }
.letter-drop:nth-child(8) { animation-delay: 0.8s; }
.letter-drop:nth-child(9) { animation-delay: 0.9s; }
.letter-drop:nth-child(10) { animation-delay: 1.0s; }

/* حركات للنوافذ المنبثقة */
.modal.show {
    animation: fadeIn 0.3s ease-out;
}

.modal.hide {
    animation: fadeOut 0.3s ease-out;
}

.modal-content {
    animation: zoomIn 0.3s ease-out;
}

/* حركات للجداول */
.data-table tr {
    animation: slideInLeft 0.3s ease-out;
}

.data-table tr:nth-child(even) {
    animation-delay: 0.1s;
}

.data-table tr:nth-child(odd) {
    animation-delay: 0.2s;
}

/* حركات للإحصائيات */
.stat-card {
    animation: slideInBottom 0.5s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* حركات للنماذج */
.form-section {
    animation: slideInRight 0.5s ease-out;
}

.form-section:nth-child(1) { animation-delay: 0.1s; }
.form-section:nth-child(2) { animation-delay: 0.2s; }
.form-section:nth-child(3) { animation-delay: 0.3s; }

/* حركات للتبويبات */
.tab-content.active {
    animation: fadeIn 0.5s ease-out;
}

.tabs-nav {
    animation: slideInTop 0.5s ease-out;
}

/* حركات للثيمات */
.theme-btn.active {
    animation: pulse 0.5s ease-out;
}

/* تأثيرات الانتقال السلس */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* حركات للأزرار */
.btn-animated {
    position: relative;
    overflow: hidden;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}

/* حركة التوهج */
@keyframes glow {
    0% {
        box-shadow: 0 0 5px currentColor;
    }
    50% {
        box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    }
    100% {
        box-shadow: 0 0 5px currentColor;
    }
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

/* حركة الموجة */
@keyframes wave {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: wave 2s infinite;
}

/* تحسينات الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
}

/* تأثيرات الانتقال المتقدمة */
.advanced-transition {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* حركات مخصصة للشاشة الافتتاحية */
.splash-screen {
    animation: gradientShift 4s ease infinite;
}

.splash-content > * {
    opacity: 0;
    animation: fadeIn 1s ease-out forwards;
}

.splash-content .app-title {
    animation-delay: 0.5s;
}

.splash-content .programmer-info {
    animation-delay: 1s;
}

.splash-content .splash-logo {
    animation-delay: 1.5s;
}

.splash-content .loading-bar {
    animation-delay: 2s;
}
