@echo off
chcp 65001 >nul
title نظام إدارة بيانات الموظفين - علي عاجل خشّان المحنّة

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ║                                                                             ║
echo ║                    🚀 نظام إدارة بيانات الموظفين 🚀                      ║
echo ║                                                                             ║
echo ║                  👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻                ║
echo ║                                                                             ║
echo ║                  🏛️ محافظة الديوانية / شعبة الرواتب 🏛️                 ║
echo ║                                                                             ║
echo ║                📧 <EMAIL>  📱 07727232639                     ║
echo ║                                                                             ║
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🔍 جاري فحص المتطلبات...
echo.

REM التحقق من وجود Node.js
echo 📋 فحص Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo.
    echo 📥 يرجى تثبيت Node.js من الرابط التالي:
    echo    https://nodejs.org
    echo.
    echo 📞 للدعم الفني:
    echo    📧 البريد الإلكتروني: <EMAIL>
    echo    📱 الهاتف/واتساب: 07727232639
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)
echo ✅ Node.js موجود ويعمل بشكل صحيح

REM التحقق من وجود ملفات المشروع
echo 📋 فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo.
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    echo 📞 للدعم الفني:
    echo    📧 البريد الإلكتروني: <EMAIL>
    echo    📱 الهاتف/واتساب: 07727232639
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

if not exist "main.js" (
    echo ❌ خطأ: ملف main.js غير موجود
    echo.
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)

if not exist "index.html" (
    echo ❌ خطأ: ملف index.html غير موجود
    echo.
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)
echo ✅ جميع ملفات المشروع موجودة

REM إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
echo 📋 فحص مجلد قاعدة البيانات...
if not exist "database_safe" (
    mkdir database_safe
    echo ✅ تم إنشاء مجلد قاعدة البيانات
) else (
    echo ✅ مجلد قاعدة البيانات موجود
)

REM التحقق من تثبيت التبعيات
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات للمرة الأولى...
    echo ⏳ هذا قد يستغرق بضع دقائق...
    echo.
    npm install --silent
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ خطأ في تثبيت التبعيات
        echo 🌐 تأكد من اتصالك بالإنترنت وحاول مرة أخرى
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح!
    echo.
)

echo ✨ تشغيل البرنامج...
echo 💡 ملاحظة: ستفتح نافذة البرنامج خلال ثوانٍ قليلة
echo 🔄 لإغلاق البرنامج، أغلق النافذة أو اضغط Ctrl+C هنا
echo.

REM تشغيل البرنامج مع إخفاء المخرجات
start /min cmd /c "npm start >nul 2>&1"

REM انتظار قليل ثم إخفاء هذه النافذة
timeout /t 3 /nobreak >nul
exit
