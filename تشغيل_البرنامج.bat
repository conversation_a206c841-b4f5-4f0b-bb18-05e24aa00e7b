@echo off
chcp 65001 >nul
title 🚀 تشغيل نظام إدارة بيانات الموظفين

REM إخفاء نافذة الأوامر بعد التشغيل
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

color 0A
echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           🏢 نظام إدارة بيانات الموظفين 🏢            ██
echo ██                                                        ██
echo ██        👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻         ██
echo ██                                                        ██
echo ██        🏛️ محافظة الديوانية / شعبة الرواتب 🏛️         ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 🚀 جاري تشغيل البرنامج...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    echo 📞 للدعم الفني:
    echo 📧 البريد الإلكتروني: <EMAIL>
    echo 📱 الهاتف/واتساب: 07727232639
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملفات المشروع
if not exist "package.json" (
    echo ❌ خطأ: ملفات المشروع غير موجودة
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    pause
    exit /b 1
)

REM إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
if not exist "database_safe" (
    mkdir database_safe
    echo ✅ تم إنشاء مجلد قاعدة البيانات
)

REM التحقق من تثبيت التبعيات
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات للمرة الأولى...
    echo ⏳ هذا قد يستغرق بضع دقائق...
    echo.
    npm install --silent
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ خطأ في تثبيت التبعيات
        echo 🌐 تأكد من اتصالك بالإنترنت وحاول مرة أخرى
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح!
    echo.
)

echo ✨ تشغيل البرنامج...
echo 💡 ملاحظة: ستفتح نافذة البرنامج خلال ثوانٍ قليلة
echo 🔄 لإغلاق البرنامج، أغلق النافذة أو اضغط Ctrl+C هنا
echo.

REM تشغيل البرنامج مع إخفاء المخرجات
start /min cmd /c "npm start >nul 2>&1"

REM انتظار قليل ثم إخفاء هذه النافذة
timeout /t 3 /nobreak >nul
exit
