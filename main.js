const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();

// إنشاء مجلد قاعدة البيانات
const dbDir = path.join(__dirname, 'database_safe');
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// مسار قاعدة البيانات
const dbPath = path.join(dbDir, 'employees.db');

let mainWindow;
let db;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.svg'),
        show: false,
        titleBarStyle: 'default',
        backgroundColor: '#667eea', // لون خلفية يتناسب مع الثيم
        frame: true,
        resizable: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        title: 'نظام إدارة بيانات الموظفين - المبرمج: علي عاجل خشّان المحنّة'
    });

    mainWindow.loadFile('index.html');

    // إظهار النافذة بعد التحميل مع تأثير سلس
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.focus();

        // إخفاء شريط القوائم إذا لم يكن في وضع التطوير
        mainWindow.setMenuBarVisibility(false);
    });

    // منع إظهار النافذة السوداء
    mainWindow.on('show', () => {
        mainWindow.webContents.executeJavaScript(`
            document.body.style.backgroundColor = 'transparent';
        `);
    });

    // فتح أدوات المطور في وضع التطوير فقط
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

function initDatabase() {
    db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
            console.error('خطأ في فتح قاعدة البيانات:', err.message);
        } else {
            console.log('تم الاتصال بقاعدة البيانات بنجاح');
            createTables();
        }
    });
}

function createTables() {
    const createEmployeesTable = `
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            year INTEGER NOT NULL,
            name TEXT NOT NULL,
            spouse_name TEXT,
            spouse_status TEXT,
            spouse_other TEXT,
            children_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    const createChildrenTable = `
        CREATE TABLE IF NOT EXISTS children (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id INTEGER,
            name TEXT NOT NULL,
            birth_day INTEGER,
            birth_month INTEGER,
            birth_year INTEGER,
            education_level TEXT,
            status TEXT,
            FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
        )
    `;

    const createYearsTable = `
        CREATE TABLE IF NOT EXISTS years (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            year INTEGER UNIQUE NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    db.run(createEmployeesTable);
    db.run(createChildrenTable);
    db.run(createYearsTable);
}

// معالجات IPC
ipcMain.handle('get-years', () => {
    return new Promise((resolve, reject) => {
        db.all('SELECT DISTINCT year FROM years ORDER BY year DESC', (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
        });
    });
});

ipcMain.handle('add-year', (event, year) => {
    return new Promise((resolve, reject) => {
        db.run('INSERT OR IGNORE INTO years (year) VALUES (?)', [year], function(err) {
            if (err) reject(err);
            else resolve({ success: true, year });
        });
    });
});

ipcMain.handle('save-employee', (event, employeeData) => {
    return new Promise((resolve, reject) => {
        const { year, name, spouse_name, spouse_status, spouse_other, children } = employeeData;

        db.run(
            'INSERT INTO employees (year, name, spouse_name, spouse_status, spouse_other, children_count) VALUES (?, ?, ?, ?, ?, ?)',
            [year, name, spouse_name, spouse_status, spouse_other, children.length],
            function(err) {
                if (err) {
                    reject(err);
                } else {
                    const employeeId = this.lastID;

                    // حفظ بيانات الأطفال
                    if (children.length > 0) {
                        const stmt = db.prepare('INSERT INTO children (employee_id, name, birth_day, birth_month, birth_year, education_level, status) VALUES (?, ?, ?, ?, ?, ?, ?)');

                        children.forEach(child => {
                            stmt.run([employeeId, child.name, child.birth_day, child.birth_month, child.birth_year, child.education_level, child.status]);
                        });

                        stmt.finalize();
                    }

                    resolve({ success: true, id: employeeId });
                }
            }
        );
    });
});

// الحصول على إحصائيات التقرير
ipcMain.handle('get-report-stats', (event, year) => {
    return new Promise((resolve, reject) => {
        const queries = {
            total: 'SELECT COUNT(*) as count FROM employees WHERE year = ?',
            complete: `SELECT COUNT(*) as count FROM employees WHERE year = ?
                      AND name IS NOT NULL AND name != ''
                      AND spouse_name IS NOT NULL AND spouse_name != ''`,
            incomplete: `SELECT COUNT(*) as count FROM employees WHERE year = ?
                        AND (name IS NULL OR name = '' OR spouse_name IS NULL OR spouse_name = '')`,
            notProvided: `SELECT COUNT(*) as count FROM employees WHERE year = ?
                         AND spouse_status = 'موظف' AND spouse_other = 'لم يزودنا'`
        };

        const stats = {};
        const promises = Object.keys(queries).map(key => {
            return new Promise((res, rej) => {
                db.get(queries[key], [year], (err, row) => {
                    if (err) rej(err);
                    else {
                        stats[key] = row.count;
                        res();
                    }
                });
            });
        });

        Promise.all(promises)
            .then(() => resolve(stats))
            .catch(reject);
    });
});

// الحصول على الموظفين حسب السنة
ipcMain.handle('get-employees-by-year', (event, year) => {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT e.*,
                   GROUP_CONCAT(c.name || '|' || c.birth_day || '|' || c.birth_month || '|' || c.birth_year || '|' || c.education_level || '|' || c.status, ';') as children_data
            FROM employees e
            LEFT JOIN children c ON e.id = c.employee_id
            WHERE e.year = ?
            GROUP BY e.id
            ORDER BY e.name
        `;

        db.all(query, [year], (err, rows) => {
            if (err) {
                reject(err);
            } else {
                // معالجة بيانات الأطفال
                const employees = rows.map(row => {
                    const employee = { ...row };
                    if (row.children_data) {
                        employee.children = row.children_data.split(';').map(childData => {
                            const [name, day, month, year, education, status] = childData.split('|');
                            return { name, birth_day: day, birth_month: month, birth_year: year, education_level: education, status };
                        });
                    } else {
                        employee.children = [];
                    }
                    delete employee.children_data;
                    return employee;
                });
                resolve(employees);
            }
        });
    });
});

app.whenReady().then(() => {
    initDatabase();
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (db) {
            db.close();
        }
        app.quit();
    }
});

// الحصول على بيانات المقارنة السنوية
ipcMain.handle('get-comparison-data', (event, currentYear) => {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT DISTINCT year FROM employees WHERE year != ? ORDER BY year DESC
        `;

        db.all(query, [currentYear], (err, years) => {
            if (err) {
                reject(err);
            } else {
                resolve(years);
            }
        });
    });
});

// حذف موظف
ipcMain.handle('delete-employee', (event, employeeId) => {
    return new Promise((resolve, reject) => {
        db.run('DELETE FROM employees WHERE id = ?', [employeeId], function(err) {
            if (err) {
                reject(err);
            } else {
                resolve({ success: true, deletedRows: this.changes });
            }
        });
    });
});

// تحديث بيانات موظف
ipcMain.handle('update-employee', (event, employeeId, employeeData) => {
    return new Promise((resolve, reject) => {
        const { name, spouse_name, spouse_status, spouse_other, children } = employeeData;

        db.run(
            'UPDATE employees SET name = ?, spouse_name = ?, spouse_status = ?, spouse_other = ?, children_count = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [name, spouse_name, spouse_status, spouse_other, children.length, employeeId],
            function(err) {
                if (err) {
                    reject(err);
                } else {
                    // حذف الأطفال القدامى
                    db.run('DELETE FROM children WHERE employee_id = ?', [employeeId], (err) => {
                        if (err) {
                            reject(err);
                        } else {
                            // إضافة الأطفال الجدد
                            if (children.length > 0) {
                                const stmt = db.prepare('INSERT INTO children (employee_id, name, birth_day, birth_month, birth_year, education_level, status) VALUES (?, ?, ?, ?, ?, ?, ?)');

                                children.forEach(child => {
                                    stmt.run([employeeId, child.name, child.birth_day, child.birth_month, child.birth_year, child.education_level, child.status]);
                                });

                                stmt.finalize();
                            }

                            resolve({ success: true, updatedRows: this.changes });
                        }
                    });
                }
            }
        );
    });
});

// البحث في الموظفين
ipcMain.handle('search-employees', (event, year, searchTerm) => {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT e.*,
                   GROUP_CONCAT(c.name || '|' || c.birth_day || '|' || c.birth_month || '|' || c.birth_year || '|' || c.education_level || '|' || c.status, ';') as children_data
            FROM employees e
            LEFT JOIN children c ON e.id = c.employee_id
            WHERE e.year = ? AND (e.name LIKE ? OR e.spouse_name LIKE ?)
            GROUP BY e.id
            ORDER BY e.name
        `;

        const searchPattern = `%${searchTerm}%`;
        db.all(query, [year, searchPattern, searchPattern], (err, rows) => {
            if (err) {
                reject(err);
            } else {
                const employees = rows.map(row => {
                    const employee = { ...row };
                    if (row.children_data) {
                        employee.children = row.children_data.split(';').map(childData => {
                            const [name, day, month, year, education, status] = childData.split('|');
                            return { name, birth_day: day, birth_month: month, birth_year: year, education_level: education, status };
                        });
                    } else {
                        employee.children = [];
                    }
                    delete employee.children_data;
                    return employee;
                });
                resolve(employees);
            }
        });
    });
});

// تفريغ قاعدة البيانات
ipcMain.handle('clear-database', () => {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            db.run('DELETE FROM children', (err) => {
                if (err) reject(err);
            });
            db.run('DELETE FROM employees', (err) => {
                if (err) reject(err);
            });
            db.run('DELETE FROM years', (err) => {
                if (err) reject(err);
                else resolve({ success: true });
            });
        });
    });
});

// نسخ احتياطي من قاعدة البيانات
ipcMain.handle('backup-database', () => {
    return new Promise((resolve, reject) => {
        const { dialog } = require('electron');
        const fs = require('fs');
        const path = require('path');

        dialog.showSaveDialog(mainWindow, {
            title: 'حفظ نسخة احتياطية',
            defaultPath: `backup_${new Date().toISOString().split('T')[0]}.db`,
            filters: [
                { name: 'Database Files', extensions: ['db'] }
            ]
        }).then(result => {
            if (!result.canceled) {
                fs.copyFile(dbPath, result.filePath, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ success: true, path: result.filePath });
                    }
                });
            } else {
                resolve({ success: false, canceled: true });
            }
        }).catch(reject);
    });
});

app.on('before-quit', () => {
    if (db) {
        db.close();
    }
});
