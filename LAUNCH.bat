@echo off

echo Employee Management System
echo Ali <PERSON>
echo.

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Node.js not found
    echo Install from https://nodejs.org
    pause
    exit /b 1
)

if not exist "package.json" (
    echo Project files not found
    pause
    exit /b 1
)

if not exist "database_safe" mkdir database_safe

if not exist "node_modules" (
    echo Installing...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo Install failed
        pause
        exit /b 1
    )
)

echo Starting program...
echo The terminal window will be hidden after launch
timeout /t 2 /nobreak >nul
start "" /min cmd /c "npm start"
exit
