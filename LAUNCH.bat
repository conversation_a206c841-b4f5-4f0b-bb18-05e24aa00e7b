@echo off

REM إخفاء النافذة نهائياً
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('Node.js not found. Install from https://nodejs.org', 'Error', 'OK', 'Error')"
    exit /b 1
)

if not exist "package.json" (
    powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('Project files not found', 'Error', 'OK', 'Error')"
    exit /b 1
)

if not exist "database_safe" mkdir database_safe

if not exist "node_modules" (
    npm install >nul 2>nul
    if %ERRORLEVEL% NEQ 0 (
        powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('Install failed', 'Error', 'OK', 'Error')"
        exit /b 1
    )
)

REM تشغيل البرنامج مع إخفاء النافذة نهائياً
powershell -WindowStyle Hidden -Command "Start-Process npm -ArgumentList 'start' -WindowStyle Hidden"
exit
