/* الثيمات المختلفة */

/* الثيم الافتراضي - البركاني */
:root,
[data-theme="volcano"] {
    --bg-gradient: linear-gradient(135deg, #2c1810 0%, #8b0000 50%, #ff4500 100%);
    --header-bg: rgba(139, 0, 0, 0.2);
    --nav-bg: rgba(139, 0, 0, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(255, 69, 0, 0.1);
    --primary-color: #8b0000;
    --primary-hover: #a50000;
    --secondary-color: #ff4500;
    --secondary-hover: #ff6500;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffa500;
    --warning-hover: #ff8c00;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #4682b4;
    --info-hover: #5f9ea0;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #fff5f5;
}

/* ثيم البنفسجي والأزرق */
[data-theme="violet-blue"] {
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #9b59b6 100%);
    --header-bg: rgba(118, 75, 162, 0.2);
    --nav-bg: rgba(118, 75, 162, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(155, 89, 182, 0.1);
    --primary-color: #764ba2;
    --primary-hover: #8e44ad;
    --secondary-color: #667eea;
    --secondary-hover: #5a67d8;
    --success-color: #27ae60;
    --success-hover: #2ecc71;
    --warning-color: #f39c12;
    --warning-hover: #e67e22;
    --danger-color: #e74c3c;
    --danger-hover: #c0392b;
    --info-color: #3498db;
    --info-hover: #2980b9;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #f8f5ff;
}

/* ثيم الفضي والأصفر */
[data-theme="silver-yellow"] {
    --bg-gradient: linear-gradient(135deg, #c0c0c0 0%, #ffd700 50%, #ffff00 100%);
    --header-bg: rgba(192, 192, 192, 0.3);
    --nav-bg: rgba(192, 192, 192, 0.3);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(255, 215, 0, 0.1);
    --primary-color: #b8860b;
    --primary-hover: #daa520;
    --secondary-color: #c0c0c0;
    --secondary-hover: #a9a9a9;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffd700;
    --warning-hover: #ffed4e;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #4682b4;
    --info-hover: #5f9ea0;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #fffef5;
}

/* ثيم السمائي */
[data-theme="sky-blue"] {
    --bg-gradient: linear-gradient(135deg, #87ceeb 0%, #4169e1 50%, #0000ff 100%);
    --header-bg: rgba(65, 105, 225, 0.2);
    --nav-bg: rgba(65, 105, 225, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(135, 206, 235, 0.1);
    --primary-color: #4169e1;
    --primary-hover: #0000ff;
    --secondary-color: #87ceeb;
    --secondary-hover: #87cefa;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffa500;
    --warning-hover: #ff8c00;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #00bfff;
    --info-hover: #1e90ff;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #f0f8ff;
}

/* ثيم الرصاصي والسماوي */
[data-theme="grey-cyan"] {
    --bg-gradient: linear-gradient(135deg, #708090 0%, #00ffff 50%, #40e0d0 100%);
    --header-bg: rgba(112, 128, 144, 0.2);
    --nav-bg: rgba(112, 128, 144, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(64, 224, 208, 0.1);
    --primary-color: #708090;
    --primary-hover: #2f4f4f;
    --secondary-color: #40e0d0;
    --secondary-hover: #00ced1;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffa500;
    --warning-hover: #ff8c00;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #00ffff;
    --info-hover: #00e5e5;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #f0ffff;
}

/* ثيم الأخضر الحشيشي */
[data-theme="grass-green"] {
    --bg-gradient: linear-gradient(135deg, #228b22 0%, #32cd32 50%, #7cfc00 100%);
    --header-bg: rgba(34, 139, 34, 0.2);
    --nav-bg: rgba(34, 139, 34, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(124, 252, 0, 0.1);
    --primary-color: #228b22;
    --primary-hover: #006400;
    --secondary-color: #32cd32;
    --secondary-hover: #00ff00;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffa500;
    --warning-hover: #ff8c00;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #4682b4;
    --info-hover: #5f9ea0;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #f0fff0;
}

/* ثيم البرتقالي */
[data-theme="orange"] {
    --bg-gradient: linear-gradient(135deg, #ff8c00 0%, #ffa500 50%, #ffff00 100%);
    --header-bg: rgba(255, 140, 0, 0.2);
    --nav-bg: rgba(255, 140, 0, 0.2);
    --card-bg: rgba(255, 255, 255, 0.95);
    --content-bg: rgba(255, 255, 255, 0.95);
    --section-bg: rgba(255, 165, 0, 0.1);
    --primary-color: #ff8c00;
    --primary-hover: #ff7f00;
    --secondary-color: #ffa500;
    --secondary-hover: #ff9500;
    --success-color: #228b22;
    --success-hover: #32cd32;
    --warning-color: #ffd700;
    --warning-hover: #ffed4e;
    --danger-color: #dc143c;
    --danger-hover: #b22222;
    --info-color: #4682b4;
    --info-hover: #5f9ea0;
    --text-color: #333;
    --border-color: #ddd;
    --hover-bg: #fff8f0;
}

/* معاينات الثيمات */
.themes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.theme-btn {
    background: white;
    border: 3px solid transparent;
    border-radius: 15px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.theme-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.theme-btn.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.theme-preview {
    width: 100%;
    height: 60px;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.volcano-preview {
    background: linear-gradient(135deg, #2c1810 0%, #8b0000 50%, #ff4500 100%);
}

.violet-blue-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #9b59b6 100%);
}

.silver-yellow-preview {
    background: linear-gradient(135deg, #c0c0c0 0%, #ffd700 50%, #ffff00 100%);
}

.sky-blue-preview {
    background: linear-gradient(135deg, #87ceeb 0%, #4169e1 50%, #0000ff 100%);
}

.grey-cyan-preview {
    background: linear-gradient(135deg, #708090 0%, #00ffff 50%, #40e0d0 100%);
}

.grass-green-preview {
    background: linear-gradient(135deg, #228b22 0%, #32cd32 50%, #7cfc00 100%);
}

.orange-preview {
    background: linear-gradient(135deg, #ff8c00 0%, #ffa500 50%, #ffff00 100%);
}

.theme-btn span {
    font-weight: 600;
    color: var(--text-color, #333);
    font-size: 0.9rem;
}

/* إعدادات خاصة */
.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: var(--section-bg, #f8f9ff);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid var(--border-color, #eee);
}

.settings-section h3 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.3rem;
}

.database-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.programmer-bio {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid var(--primary-color, #667eea);
}

.programmer-title {
    color: var(--primary-color, #667eea);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.bio-text {
    color: var(--text-color, #666);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.achievements h5 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.achievements ul {
    list-style: none;
    padding: 0;
}

.achievements li {
    padding: 0.5rem 0;
    color: var(--text-color, #666);
    position: relative;
    padding-right: 1.5rem;
}

.achievements li:before {
    content: "✓";
    color: var(--success-color, #28a745);
    font-weight: bold;
    position: absolute;
    right: 0;
}

.copyright {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-color, #eee);
    text-align: center;
}

.copyright p {
    color: var(--text-color, #666);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.copyright i {
    color: var(--primary-color, #667eea);
}

/* تأثيرات الانتقال بين الثيمات */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* الاستجابة للثيمات */
@media (max-width: 768px) {
    .themes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .database-actions {
        flex-direction: column;
    }
    
    .theme-btn {
        padding: 0.8rem;
    }
    
    .theme-preview {
        height: 40px;
    }
}
