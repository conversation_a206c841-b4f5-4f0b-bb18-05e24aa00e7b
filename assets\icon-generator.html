<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أيقونات البرنامج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .icon-size {
            text-align: center;
            background: #f8f9ff;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #eee;
        }
        
        .icon-size h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .icon-size svg {
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }
        
        .download-section {
            background: #f0f8ff;
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .download-btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .info-section {
            background: #fff3cd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .programmer-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 10px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
            flex-wrap: wrap;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #667eea;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 مولد أيقونات نظام إدارة بيانات الموظفين</h1>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16 (صغير)</h3>
                <svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                    <!-- نسخة مبسطة للحجم الصغير -->
                    <defs>
                        <linearGradient id="bg16" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea"/>
                            <stop offset="100%" style="stop-color:#8b0000"/>
                        </linearGradient>
                    </defs>
                    <circle cx="128" cy="128" r="120" fill="url(#bg16)"/>
                    <rect x="88" y="118" width="80" height="20" rx="4" fill="#ffd700"/>
                </svg>
            </div>
            
            <div class="icon-size">
                <h3>32x32 (متوسط)</h3>
                <svg width="32" height="32" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="bg32" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea"/>
                            <stop offset="50%" style="stop-color:#764ba2"/>
                            <stop offset="100%" style="stop-color:#8b0000"/>
                        </linearGradient>
                    </defs>
                    <circle cx="128" cy="128" r="120" fill="url(#bg32)" stroke="#fff" stroke-width="4"/>
                    <rect x="88" y="110" width="80" height="8" fill="#ffd700"/>
                    <rect x="108" y="118" width="8" height="20" fill="#ffd700"/>
                    <rect x="128" y="118" width="8" height="20" fill="#ffd700"/>
                    <rect x="148" y="118" width="8" height="20" fill="#ffd700"/>
                </svg>
            </div>
            
            <div class="icon-size">
                <h3>64x64 (كبير)</h3>
                <svg width="64" height="64" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="bg64" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea"/>
                            <stop offset="50%" style="stop-color:#764ba2"/>
                            <stop offset="100%" style="stop-color:#8b0000"/>
                        </linearGradient>
                        <linearGradient id="icon64" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffd700"/>
                            <stop offset="100%" style="stop-color:#ffc107"/>
                        </linearGradient>
                    </defs>
                    <circle cx="128" cy="128" r="120" fill="url(#bg64)" stroke="#fff" stroke-width="4"/>
                    <rect x="88" y="110" width="80" height="8" fill="url(#icon64)"/>
                    <rect x="93" y="90" width="8" height="20" fill="url(#icon64)"/>
                    <rect x="108" y="90" width="8" height="20" fill="url(#icon64)"/>
                    <rect x="123" y="90" width="8" height="20" fill="url(#icon64)"/>
                    <rect x="138" y="90" width="8" height="20" fill="url(#icon64)"/>
                    <rect x="153" y="90" width="8" height="20" fill="url(#icon64)"/>
                    <polygon points="83,90 128,70 173,90" fill="url(#icon64)"/>
                    <circle cx="108" cy="150" r="6" fill="url(#icon64)"/>
                    <circle cx="128" cy="150" r="6" fill="url(#icon64)"/>
                    <circle cx="148" cy="150" r="6" fill="url(#icon64)"/>
                </svg>
            </div>
            
            <div class="icon-size">
                <h3>128x128 (كبير جداً)</h3>
                <svg width="128" height="128" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                    <!-- الأيقونة الكاملة -->
                    <defs>
                        <linearGradient id="backgroundGradient128" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#8b0000;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="iconGradient128" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffc107;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <circle cx="128" cy="128" r="120" fill="url(#backgroundGradient128)" stroke="#ffffff" stroke-width="4"/>
                    <circle cx="128" cy="128" r="100" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                    <g transform="translate(128, 80)">
                        <rect x="-40" y="30" width="80" height="8" fill="url(#iconGradient128)"/>
                        <rect x="-35" y="10" width="8" height="20" fill="url(#iconGradient128)"/>
                        <rect x="-20" y="10" width="8" height="20" fill="url(#iconGradient128)"/>
                        <rect x="-5" y="10" width="8" height="20" fill="url(#iconGradient128)"/>
                        <rect x="10" y="10" width="8" height="20" fill="url(#iconGradient128)"/>
                        <rect x="25" y="10" width="8" height="20" fill="url(#iconGradient128)"/>
                        <polygon points="-45,10 0,-10 45,10" fill="url(#iconGradient128)"/>
                        <polygon points="0,-5 2,0 7,0 3,3 5,8 0,5 -5,8 -3,3 -7,0 -2,0" fill="#ffffff"/>
                    </g>
                    <g transform="translate(128, 160)">
                        <g transform="translate(-20, 0)">
                            <circle cx="0" cy="-10" r="6" fill="url(#iconGradient128)"/>
                            <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient128)"/>
                        </g>
                        <g transform="translate(0, 0)">
                            <circle cx="0" cy="-10" r="6" fill="url(#iconGradient128)"/>
                            <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient128)"/>
                        </g>
                        <g transform="translate(20, 0)">
                            <circle cx="0" cy="-10" r="6" fill="url(#iconGradient128)"/>
                            <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient128)"/>
                        </g>
                    </g>
                </svg>
            </div>
        </div>
        
        <div class="download-section">
            <h3>📥 تحميل الأيقونات</h3>
            <p>اضغط على الأزرار أدناه لتحميل الأيقونات بصيغ مختلفة:</p>
            
            <button class="download-btn" onclick="downloadSVG()">📄 تحميل SVG</button>
            <button class="download-btn" onclick="downloadPNG(16)">🖼️ PNG 16x16</button>
            <button class="download-btn" onclick="downloadPNG(32)">🖼️ PNG 32x32</button>
            <button class="download-btn" onclick="downloadPNG(64)">🖼️ PNG 64x64</button>
            <button class="download-btn" onclick="downloadPNG(128)">🖼️ PNG 128x128</button>
            <button class="download-btn" onclick="downloadPNG(256)">🖼️ PNG 256x256</button>
            <button class="download-btn" onclick="downloadICO()">⚙️ ICO (Windows)</button>
        </div>
        
        <div class="info-section">
            <h4>📋 معلومات الأيقونة</h4>
            <ul>
                <li><strong>التصميم:</strong> أيقونة عصرية تمثل نظام إدارة الموظفين</li>
                <li><strong>الألوان:</strong> تدرج من الأزرق إلى البنفسجي إلى الأحمر الداكن</li>
                <li><strong>العناصر:</strong> مبنى حكومي + أشخاص + ملفات + إعدادات</li>
                <li><strong>الاستخدام:</strong> أيقونة التطبيق وملفات التشغيل</li>
                <li><strong>التوافق:</strong> جميع أنظمة التشغيل</li>
            </ul>
        </div>
        
        <div class="programmer-info">
            <h3>👨‍💻 معلومات المبرمج</h3>
            <p><strong>علي عاجل خشّان المحنّة</strong></p>
            <p>محافظة الديوانية / شعبة الرواتب</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📱</span>
                    <span>07727232639</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadSVG() {
            // قراءة ملف SVG الأصلي
            fetch('icon.svg')
                .then(response => response.text())
                .then(svgContent => {
                    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'employee-management-icon.svg';
                    a.click();
                    URL.revokeObjectURL(url);
                });
        }
        
        function downloadPNG(size) {
            const svg = document.querySelector(`svg[width="${size}"]`) || document.querySelector('svg[width="128"]');
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            canvas.width = size;
            canvas.height = size;
            
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                canvas.toBlob(function(blob) {
                    const downloadUrl = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = `employee-management-icon-${size}x${size}.png`;
                    a.click();
                    URL.revokeObjectURL(downloadUrl);
                });
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }
        
        function downloadICO() {
            alert('لتحويل PNG إلى ICO، يمكنك استخدام أدوات التحويل المجانية عبر الإنترنت مثل:\n\n• convertio.co\n• online-convert.com\n• favicon.io\n\nأو استخدم برامج تحرير الصور مثل GIMP أو Photoshop');
        }
    </script>
</body>
</html>
