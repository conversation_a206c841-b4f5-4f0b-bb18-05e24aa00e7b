// إدارة الثيمات والألوان
class ThemeManager {
    
    constructor() {
        this.themes = {
            'volcano': {
                name: 'البركاني',
                colors: {
                    primary: '#8b0000',
                    secondary: '#ff4500',
                    background: 'linear-gradient(135deg, #2c1810 0%, #8b0000 50%, #ff4500 100%)',
                    text: '#333',
                    accent: '#dc143c'
                }
            },
            'violet-blue': {
                name: 'البنفسجي والأزرق',
                colors: {
                    primary: '#764ba2',
                    secondary: '#667eea',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #9b59b6 100%)',
                    text: '#333',
                    accent: '#3498db'
                }
            },
            'silver-yellow': {
                name: 'الفضي والأصفر',
                colors: {
                    primary: '#b8860b',
                    secondary: '#c0c0c0',
                    background: 'linear-gradient(135deg, #c0c0c0 0%, #ffd700 50%, #ffff00 100%)',
                    text: '#333',
                    accent: '#ffd700'
                }
            },
            'sky-blue': {
                name: 'السمائي',
                colors: {
                    primary: '#4169e1',
                    secondary: '#87ceeb',
                    background: 'linear-gradient(135deg, #87ceeb 0%, #4169e1 50%, #0000ff 100%)',
                    text: '#333',
                    accent: '#00bfff'
                }
            },
            'grey-cyan': {
                name: 'الرصاصي والسماوي',
                colors: {
                    primary: '#708090',
                    secondary: '#40e0d0',
                    background: 'linear-gradient(135deg, #708090 0%, #00ffff 50%, #40e0d0 100%)',
                    text: '#333',
                    accent: '#00ffff'
                }
            },
            'grass-green': {
                name: 'الأخضر الحشيشي',
                colors: {
                    primary: '#228b22',
                    secondary: '#32cd32',
                    background: 'linear-gradient(135deg, #228b22 0%, #32cd32 50%, #7cfc00 100%)',
                    text: '#333',
                    accent: '#00ff00'
                }
            },
            'orange': {
                name: 'البرتقالي',
                colors: {
                    primary: '#ff8c00',
                    secondary: '#ffa500',
                    background: 'linear-gradient(135deg, #ff8c00 0%, #ffa500 50%, #ffff00 100%)',
                    text: '#333',
                    accent: '#ffd700'
                }
            }
        };
        
        this.currentTheme = 'volcano';
        this.init();
    }
    
    // تهيئة مدير الثيمات
    init() {
        this.loadSavedTheme();
        this.setupThemeButtons();
    }
    
    // تحميل الثيم المحفوظ
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('selectedTheme');
        if (savedTheme && this.themes[savedTheme]) {
            this.applyTheme(savedTheme);
        }
    }
    
    // إعداد أزرار الثيمات
    setupThemeButtons() {
        const themeButtons = document.querySelectorAll('.theme-btn');
        themeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const theme = button.dataset.theme;
                this.changeTheme(theme);
            });
        });
    }
    
    // تغيير الثيم
    changeTheme(themeName) {
        if (!this.themes[themeName]) return;
        
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // إضافة الفئة النشطة للثيم المحدد
        const activeButton = document.querySelector(`[data-theme="${themeName}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
        
        // تطبيق الثيم
        this.applyTheme(themeName);
        
        // حفظ الثيم
        this.saveTheme(themeName);
        
        // إظهار رسالة النجاح
        if (window.showMessage) {
            window.showMessage('نجح', `تم تطبيق ثيم ${this.themes[themeName].name} بنجاح`);
        }
    }
    
    // تطبيق الثيم
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return;
        
        this.currentTheme = themeName;
        
        // تطبيق الثيم على الجسم
        document.body.setAttribute('data-theme', themeName);
        
        // تطبيق الألوان المخصصة
        const root = document.documentElement;
        Object.entries(theme.colors).forEach(([property, value]) => {
            root.style.setProperty(`--theme-${property}`, value);
        });
        
        // إضافة تأثيرات الانتقال
        this.addTransitionEffects();
    }
    
    // إضافة تأثيرات الانتقال
    addTransitionEffects() {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            element.style.transition = 'all 0.3s ease';
        });
        
        // إزالة التأثيرات بعد الانتهاء
        setTimeout(() => {
            elements.forEach(element => {
                element.style.transition = '';
            });
        }, 300);
    }
    
    // حفظ الثيم
    saveTheme(themeName) {
        localStorage.setItem('selectedTheme', themeName);
    }
    
    // الحصول على الثيم الحالي
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    // الحصول على ألوان الثيم الحالي
    getCurrentColors() {
        return this.themes[this.currentTheme].colors;
    }
    
    // إنشاء ثيم مخصص
    createCustomTheme(name, colors) {
        this.themes[name] = {
            name: name,
            colors: colors
        };
    }
    
    // حذف ثيم مخصص
    deleteCustomTheme(name) {
        if (this.themes[name] && name !== 'volcano') {
            delete this.themes[name];
            if (this.currentTheme === name) {
                this.changeTheme('volcano');
            }
        }
    }
}

// إدارة الحركات والتأثيرات
class AnimationManager {
    
    constructor() {
        this.animationQueue = [];
        this.isAnimating = false;
    }
    
    // إضافة حركة للطابور
    addAnimation(element, animationClass, duration = 1000) {
        this.animationQueue.push({
            element,
            animationClass,
            duration
        });
        
        if (!this.isAnimating) {
            this.processQueue();
        }
    }
    
    // معالجة طابور الحركات
    async processQueue() {
        this.isAnimating = true;
        
        while (this.animationQueue.length > 0) {
            const animation = this.animationQueue.shift();
            await this.playAnimation(animation);
        }
        
        this.isAnimating = false;
    }
    
    // تشغيل حركة واحدة
    playAnimation({ element, animationClass, duration }) {
        return new Promise((resolve) => {
            element.classList.add(animationClass);
            
            setTimeout(() => {
                element.classList.remove(animationClass);
                resolve();
            }, duration);
        });
    }
    
    // حركة الظهور التدريجي
    fadeIn(element, duration = 500) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    // حركة الاختفاء التدريجي
    fadeOut(element, duration = 500) {
        element.style.transition = `opacity ${duration}ms ease`;
        element.style.opacity = '0';
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.display = 'none';
                resolve();
            }, duration);
        });
    }
    
    // حركة الانزلاق من اليمين
    slideInRight(element, duration = 500) {
        element.style.transform = 'translateX(100%)';
        element.style.transition = `transform ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.transform = 'translateX(0)';
        }, 10);
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    // حركة الانزلاق من اليسار
    slideInLeft(element, duration = 500) {
        element.style.transform = 'translateX(-100%)';
        element.style.transition = `transform ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.transform = 'translateX(0)';
        }, 10);
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    // حركة التكبير
    zoomIn(element, duration = 500) {
        element.style.transform = 'scale(0)';
        element.style.transition = `transform ${duration}ms ease`;
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 10);
        
        return new Promise(resolve => {
            setTimeout(resolve, duration);
        });
    }
    
    // حركة النبض
    pulse(element, duration = 1000) {
        element.style.animation = `pulse ${duration}ms ease-in-out`;
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }
    
    // حركة الاهتزاز
    shake(element, duration = 500) {
        element.style.animation = `shake ${duration}ms ease-in-out`;
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }
    
    // حركة الارتداد
    bounce(element, duration = 1000) {
        element.style.animation = `bounce ${duration}ms ease-in-out`;
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }
    
    // حركة الدوران
    rotate(element, degrees = 360, duration = 1000) {
        element.style.transition = `transform ${duration}ms ease`;
        element.style.transform = `rotate(${degrees}deg)`;
        
        return new Promise(resolve => {
            setTimeout(() => {
                element.style.transform = '';
                resolve();
            }, duration);
        });
    }
    
    // حركة مخصصة للنصوص المتساقطة
    createFallingText(container, text, delay = 100) {
        container.innerHTML = '';
        
        const letters = text.split('');
        letters.forEach((letter, index) => {
            const span = document.createElement('span');
            span.textContent = letter === ' ' ? '\u00A0' : letter;
            span.className = 'letter-drop';
            span.style.animationDelay = `${index * delay}ms`;
            container.appendChild(span);
        });
    }
    
    // حركة الكتابة
    typeWriter(element, text, speed = 100) {
        element.innerHTML = '';
        let i = 0;
        
        return new Promise(resolve => {
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else {
                    resolve();
                }
            }
            type();
        });
    }
    
    // تأثير الموجة على الأزرار
    addRippleEffect(button) {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }
    
    // إضافة تأثيرات التمرير
    addScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        });
        
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }
}

// تهيئة المديرين
const themeManager = new ThemeManager();
const animationManager = new AnimationManager();

// تصدير للاستخدام العام
window.themeManager = themeManager;
window.animationManager = animationManager;
