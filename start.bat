@echo off
chcp 65001 >nul
title نظام إدارة بيانات الموظفين - المبرمج: علي عاجل خشّان المحنّة

echo.
echo ================================================
echo    نظام إدارة بيانات الموظفين
echo    المبرمج: علي عاجل خشّان المحنّة
echo    محافظة الديوانية / شعبة الرواتب
echo ================================================
echo.

echo جاري تشغيل البرنامج...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من الموقع الرسمي: https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملفات المشروع
if not exist "package.json" (
    echo خطأ: ملفات المشروع غير موجودة
    echo تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    pause
    exit /b 1
)

REM التحقق من تثبيت التبعيات
if not exist "node_modules" (
    echo جاري تثبيت التبعيات للمرة الأولى...
    echo هذا قد يستغرق بضع دقائق...
    echo.
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo خطأ في تثبيت التبعيات
        echo تأكد من اتصالك بالإنترنت وحاول مرة أخرى
        echo.
        pause
        exit /b 1
    )
    echo.
    echo تم تثبيت التبعيات بنجاح!
    echo.
)

REM إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
if not exist "database_safe" (
    mkdir database_safe
    echo تم إنشاء مجلد قاعدة البيانات
)

echo تشغيل البرنامج...
echo.
echo ملاحظة: لإغلاق البرنامج، أغلق النافذة أو اضغط Ctrl+C هنا
echo.

REM تشغيل البرنامج مع إخفاء النافذة بعد التشغيل
echo سيتم إخفاء نافذة الأوامر بعد تشغيل البرنامج...
timeout /t 2 /nobreak >nul

start "" /min cmd /c "npm start"

REM إخفاء النافذة الحالية
powershell -WindowStyle Hidden -Command "Start-Sleep -Seconds 3; Get-Process -Name cmd | Where-Object {$_.MainWindowTitle -like '*نظام إدارة بيانات الموظفين*'} | ForEach-Object {$_.CloseMainWindow()}"

exit
