// ملف إدارة قاعدة البيانات
const { ipc<PERSON>ender<PERSON> } = require('electron');

// وظائف قاعدة البيانات
class DatabaseManager {
    
    // الحصول على إحصائيات التقرير
    static async getReportStats(year) {
        try {
            return await ipcRenderer.invoke('get-report-stats', year);
        } catch (error) {
            console.error('خطأ في الحصول على إحصائيات التقرير:', error);
            throw error;
        }
    }
    
    // الحصول على جميع الموظفين لسنة معينة
    static async getEmployeesByYear(year) {
        try {
            return await ipcRenderer.invoke('get-employees-by-year', year);
        } catch (error) {
            console.error('خطأ في الحصول على بيانات الموظفين:', error);
            throw error;
        }
    }
    
    // الحصول على بيانات المقارنة
    static async getComparisonData(currentYear) {
        try {
            return await ipcRenderer.invoke('get-comparison-data', currentYear);
        } catch (error) {
            console.error('خطأ في الحصول على بيانات المقارنة:', error);
            throw error;
        }
    }
    
    // حذف موظف
    static async deleteEmployee(employeeId) {
        try {
            return await ipcRenderer.invoke('delete-employee', employeeId);
        } catch (error) {
            console.error('خطأ في حذف الموظف:', error);
            throw error;
        }
    }
    
    // تحديث بيانات موظف
    static async updateEmployee(employeeId, employeeData) {
        try {
            return await ipcRenderer.invoke('update-employee', employeeId, employeeData);
        } catch (error) {
            console.error('خطأ في تحديث بيانات الموظف:', error);
            throw error;
        }
    }
    
    // البحث في الموظفين
    static async searchEmployees(year, searchTerm) {
        try {
            return await ipcRenderer.invoke('search-employees', year, searchTerm);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            throw error;
        }
    }
    
    // تصدير البيانات
    static async exportData(year, format = 'excel') {
        try {
            return await ipcRenderer.invoke('export-data', year, format);
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            throw error;
        }
    }
    
    // تفريغ قاعدة البيانات
    static async clearDatabase() {
        try {
            return await ipcRenderer.invoke('clear-database');
        } catch (error) {
            console.error('خطأ في تفريغ قاعدة البيانات:', error);
            throw error;
        }
    }
    
    // نسخ احتياطي من قاعدة البيانات
    static async backupDatabase() {
        try {
            return await ipcRenderer.invoke('backup-database');
        } catch (error) {
            console.error('خطأ في إنشاء نسخة احتياطية:', error);
            throw error;
        }
    }
    
    // استعادة قاعدة البيانات
    static async restoreDatabase(backupPath) {
        try {
            return await ipcRenderer.invoke('restore-database', backupPath);
        } catch (error) {
            console.error('خطأ في استعادة قاعدة البيانات:', error);
            throw error;
        }
    }
}

// وظائف مساعدة للجداول
class TableManager {
    
    // إنشاء جدول HTML
    static createTable(data, columns, tableId) {
        const tableContainer = document.getElementById(tableId);
        if (!tableContainer) return;
        
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // إنشاء رأس الجدول
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title;
            th.style.width = column.width || 'auto';
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // إنشاء جسم الجدول
        const tbody = document.createElement('tbody');
        
        if (data.length === 0) {
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = columns.length;
            emptyCell.textContent = 'لا توجد بيانات';
            emptyCell.style.textAlign = 'center';
            emptyCell.style.padding = '2rem';
            emptyRow.appendChild(emptyCell);
            tbody.appendChild(emptyRow);
        } else {
            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.style.animationDelay = `${index * 0.1}s`;
                
                columns.forEach(column => {
                    const td = document.createElement('td');
                    
                    if (column.render) {
                        td.innerHTML = column.render(row[column.key], row, index);
                    } else {
                        td.textContent = row[column.key] || '';
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }
        
        table.appendChild(tbody);
        tableContainer.innerHTML = '';
        tableContainer.appendChild(table);
        
        // إضافة وظائف الفرز
        this.addSortingFeature(table, data, columns);
    }
    
    // إضافة ميزة الفرز
    static addSortingFeature(table, data, columns) {
        const headers = table.querySelectorAll('th');
        
        headers.forEach((header, index) => {
            if (columns[index].sortable !== false) {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, data, columns, index);
                });
                
                // إضافة أيقونة الفرز
                const sortIcon = document.createElement('i');
                sortIcon.className = 'fas fa-sort sort-icon';
                header.appendChild(sortIcon);
            }
        });
    }
    
    // فرز الجدول
    static sortTable(table, data, columns, columnIndex) {
        const column = columns[columnIndex];
        const isAscending = !table.dataset.sortAsc || table.dataset.sortAsc === 'false';
        
        // إزالة أيقونات الفرز السابقة
        table.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });
        
        // تحديث أيقونة الفرز الحالية
        const currentIcon = table.querySelectorAll('th')[columnIndex].querySelector('.sort-icon');
        currentIcon.className = `fas fa-sort-${isAscending ? 'up' : 'down'} sort-icon`;
        
        // فرز البيانات
        const sortedData = [...data].sort((a, b) => {
            let aVal = a[column.key];
            let bVal = b[column.key];
            
            // تحويل للأرقام إذا كانت رقمية
            if (!isNaN(aVal) && !isNaN(bVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            }
            
            if (aVal < bVal) return isAscending ? -1 : 1;
            if (aVal > bVal) return isAscending ? 1 : -1;
            return 0;
        });
        
        // إعادة إنشاء الجدول
        this.createTable(sortedData, columns, table.parentElement.id);
        
        // حفظ حالة الفرز
        table.dataset.sortAsc = isAscending.toString();
    }
    
    // إضافة ميزة البحث
    static addSearchFeature(tableId, searchInputId) {
        const searchInput = document.getElementById(searchInputId);
        const table = document.querySelector(`#${tableId} table`);
        
        if (!searchInput || !table) return;
        
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
    
    // تصدير الجدول إلى CSV
    static exportToCSV(tableId, filename) {
        const table = document.querySelector(`#${tableId} table`);
        if (!table) return;
        
        let csv = '';
        const rows = table.querySelectorAll('tr');
        
        rows.forEach(row => {
            const cols = row.querySelectorAll('td, th');
            const rowData = Array.from(cols).map(col => {
                return '"' + col.textContent.replace(/"/g, '""') + '"';
            });
            csv += rowData.join(',') + '\n';
        });
        
        // تحميل الملف
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename + '.csv';
        link.click();
    }
    
    // طباعة الجدول
    static printTable(tableId, title) {
        const table = document.querySelector(`#${tableId} table`);
        if (!table) return;
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>${title}</title>
                    <style>
                        body { font-family: 'Cairo', sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                        th { background-color: #667eea; color: white; }
                        h1 { text-align: center; color: #667eea; }
                        .print-info { text-align: center; margin-bottom: 20px; color: #666; }
                    </style>
                </head>
                <body>
                    <h1>${title}</h1>
                    <div class="print-info">
                        تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}
                    </div>
                    ${table.outerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// تصدير الكلاسات للاستخدام العام
window.DatabaseManager = DatabaseManager;
window.TableManager = TableManager;
