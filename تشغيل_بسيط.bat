@echo off
chcp 65001 >nul

echo نظام إدارة بيانات الموظفين
echo المبرمج: علي عاجل خشّان المحنّة
echo محافظة الديوانية / شعبة الرواتب
echo.

echo جاري فحص Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)
echo Node.js موجود

echo جاري فحص ملفات المشروع...
if not exist "package.json" (
    echo خطأ: ملف package.json غير موجود
    echo تأكد من وضع الملف في مجلد البرنامج
    pause
    exit /b 1
)
echo ملفات المشروع موجودة

if not exist "database_safe" (
    mkdir database_safe
    echo تم إنشاء مجلد قاعدة البيانات
)

if not exist "node_modules" (
    echo جاري تثبيت التبعيات...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo تم تثبيت التبعيات
)

echo.
echo جاري تشغيل البرنامج...
echo ستفتح نافذة البرنامج خلال ثوانٍ
echo.

npm start

echo.
echo تم إغلاق البرنامج
pause
