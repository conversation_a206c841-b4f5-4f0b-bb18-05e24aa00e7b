@echo off

echo Employee Management System
echo Programmer: <PERSON>
echo Diwaniyah Governorate / Payroll Department
echo Email: <EMAIL>
echo Phone: 07727232639
echo.

echo Checking Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)
echo Node.js found

echo Checking project files...
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure you are in the correct directory
    echo.
    pause
    exit /b 1
)
echo Project files found

if not exist "database_safe" (
    mkdir database_safe
    echo Database folder created
)

if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes...
    echo.
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to install dependencies
        echo Check your internet connection
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
)

echo.
echo Starting the application...
echo The program window will open shortly
echo Do not close this window while the program is running
echo.

npm start

echo.
echo Program closed
echo Thank you for using Employee Management System
echo.
pause
