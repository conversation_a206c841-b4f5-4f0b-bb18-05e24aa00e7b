@echo off

REM إخفاء النافذة نهائياً من البداية
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

REM التحقق من وجود Node.js بصمت
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    powershell -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('Node.js غير مثبت على النظام. يرجى تثبيت Node.js من https://nodejs.org', 'خطأ', 'OK', 'Error')"
    exit /b 1
)

REM التحقق من وجود ملفات المشروع بصمت
if not exist "package.json" (
    powershell -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('ملفات المشروع غير موجودة', 'خطأ', 'OK', 'Error')"
    exit /b 1
)

REM إنشاء مجلد قاعدة البيانات بصمت
if not exist "database_safe" mkdir database_safe >nul 2>nul

REM تثبيت التبعيات بصمت إذا لم تكن مثبتة
if not exist "node_modules" (
    npm install >nul 2>nul
    if %ERRORLEVEL% NEQ 0 (
        powershell -WindowStyle Hidden -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('فشل في تثبيت التبعيات. تأكد من اتصالك بالإنترنت', 'خطأ', 'OK', 'Error')"
        exit /b 1
    )
)

REM تشغيل البرنامج مع إخفاء كامل للنوافذ
powershell -WindowStyle Hidden -Command "Start-Process -FilePath 'npm' -ArgumentList 'start' -WindowStyle Hidden -CreateNoWindow"

REM إنهاء النافذة الحالية فوراً
exit
