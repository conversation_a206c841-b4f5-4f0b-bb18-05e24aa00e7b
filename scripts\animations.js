// ملف الحركات والتأثيرات البصرية المتقدمة

// إدارة حركات شاشة البداية
class SplashAnimations {
    
    static initializeSplash() {
        const splashScreen = document.getElementById('splashScreen');
        const appTitle = document.getElementById('appTitle');
        const programmerName = document.getElementById('programmerName');
        const animatedIcon = document.querySelector('.animated-icon');
        const loadingBar = document.querySelector('.loading-progress');
        
        // تشغيل تسلسل الحركات
        this.animateTitle(appTitle);
        setTimeout(() => this.animateProgrammerName(programmerName), 1500);
        setTimeout(() => this.animateIcon(animatedIcon), 2500);
        setTimeout(() => this.animateLoading(loadingBar), 3000);
    }
    
    static animateTitle(element) {
        const titleText = 'نظام إدارة بيانات الموظفين';
        this.typeWriterEffect(element, titleText, 80);
    }
    
    static animateProgrammerName(element) {
        const programmerText = 'المبرمج: علي عاجل خشّان المحنّة';
        this.fallingLettersEffect(element, programmerText);
    }
    
    static animateIcon(element) {
        if (element) {
            element.style.animation = 'zoomIn 1s ease-out, pulse 2s infinite 1s';
        }
    }
    
    static animateLoading(element) {
        if (element) {
            element.style.animation = 'loading 2s ease-in-out';
        }
    }
    
    static typeWriterEffect(element, text, speed) {
        element.innerHTML = '';
        let i = 0;
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }
    
    static fallingLettersEffect(element, text) {
        element.innerHTML = '';
        
        text.split('').forEach((letter, index) => {
            const span = document.createElement('span');
            span.textContent = letter === ' ' ? '\u00A0' : letter;
            span.className = 'letter-drop';
            span.style.animationDelay = `${index * 0.1}s`;
            element.appendChild(span);
        });
    }
}

// إدارة حركات الواجهة
class InterfaceAnimations {
    
    static animatePageTransition(fromElement, toElement) {
        return new Promise((resolve) => {
            // إخفاء العنصر الحالي
            fromElement.classList.add('fade-out');
            
            setTimeout(() => {
                fromElement.classList.add('hidden');
                fromElement.classList.remove('fade-out');
                
                // إظهار العنصر الجديد
                toElement.classList.remove('hidden');
                toElement.classList.add('fade-in');
                
                setTimeout(() => {
                    toElement.classList.remove('fade-in');
                    resolve();
                }, 500);
            }, 300);
        });
    }
    
    static animateTabSwitch(activeTab, newTab) {
        // إخفاء التبويب النشط
        activeTab.classList.remove('active');
        activeTab.classList.add('slide-out-left');
        
        setTimeout(() => {
            activeTab.classList.remove('slide-out-left');
            
            // إظهار التبويب الجديد
            newTab.classList.add('active', 'slide-in-right');
            
            setTimeout(() => {
                newTab.classList.remove('slide-in-right');
            }, 500);
        }, 250);
    }
    
    static animateFormSubmission(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // تغيير النص وإضافة حركة التحميل
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;
        
        return {
            complete: () => {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> تم الحفظ';
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            },
            error: () => {
                submitBtn.innerHTML = '<i class="fas fa-times"></i> خطأ';
                submitBtn.classList.add('btn-danger');
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.classList.remove('btn-danger');
                    submitBtn.disabled = false;
                }, 2000);
            }
        };
    }
    
    static animateStatCards() {
        const statCards = document.querySelectorAll('.stat-card');
        
        statCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    static animateTableRows(table) {
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-30px)';
            
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }
    
    static animateModalShow(modal) {
        modal.classList.remove('hidden');
        modal.style.opacity = '0';
        modal.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
            modal.style.transition = 'all 0.3s ease';
            modal.style.opacity = '1';
            modal.style.transform = 'scale(1)';
        }, 10);
    }
    
    static animateModalHide(modal) {
        modal.style.transition = 'all 0.3s ease';
        modal.style.opacity = '0';
        modal.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
            modal.classList.add('hidden');
            modal.style.transform = '';
            modal.style.opacity = '';
        }, 300);
    }
}

// إدارة التأثيرات التفاعلية
class InteractiveEffects {
    
    static addHoverEffects() {
        // تأثيرات الأزرار
        document.querySelectorAll('.btn').forEach(btn => {
            this.addButtonHoverEffect(btn);
        });
        
        // تأثيرات البطاقات
        document.querySelectorAll('.stat-card, .year-selection-card, .form-section').forEach(card => {
            this.addCardHoverEffect(card);
        });
        
        // تأثيرات التبويبات
        document.querySelectorAll('.tab-btn').forEach(tab => {
            this.addTabHoverEffect(tab);
        });
    }
    
    static addButtonHoverEffect(button) {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = '';
            button.style.boxShadow = '';
        });
        
        button.addEventListener('click', () => {
            this.addRippleEffect(button, event);
        });
    }
    
    static addCardHoverEffect(card) {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 30px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
            card.style.boxShadow = '';
        });
    }
    
    static addTabHoverEffect(tab) {
        tab.addEventListener('mouseenter', () => {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = 'rgba(255,255,255,0.1)';
            }
        });
        
        tab.addEventListener('mouseleave', () => {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = '';
            }
        });
    }
    
    static addRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    
    static addScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });
        
        // مراقبة العناصر القابلة للحركة
        document.querySelectorAll('.form-section, .stat-card, .settings-section').forEach(el => {
            el.classList.add('animate-on-scroll');
            observer.observe(el);
        });
    }
    
    static addParallaxEffect() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');
            
            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }
}

// إدارة حركات البيانات
class DataAnimations {
    
    static animateNumberCounter(element, targetNumber, duration = 2000) {
        const startNumber = 0;
        const increment = targetNumber / (duration / 16);
        let currentNumber = startNumber;
        
        const timer = setInterval(() => {
            currentNumber += increment;
            
            if (currentNumber >= targetNumber) {
                currentNumber = targetNumber;
                clearInterval(timer);
            }
            
            element.textContent = Math.floor(currentNumber);
        }, 16);
    }
    
    static animateProgressBar(progressBar, percentage, duration = 1000) {
        progressBar.style.width = '0%';
        
        setTimeout(() => {
            progressBar.style.transition = `width ${duration}ms ease`;
            progressBar.style.width = percentage + '%';
        }, 100);
    }
    
    static animateChartData(chartContainer, data) {
        // حركة للرسوم البيانية (يمكن توسيعها لاحقاً)
        const bars = chartContainer.querySelectorAll('.chart-bar');
        
        bars.forEach((bar, index) => {
            const height = data[index] || 0;
            bar.style.height = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'height 1s ease';
                bar.style.height = height + '%';
            }, index * 100);
        });
    }
    
    static animateTableLoad(table) {
        const rows = table.querySelectorAll('tr');
        
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }
}

// إدارة حركات الإشعارات
class NotificationAnimations {
    
    static showSuccessNotification(message) {
        this.createNotification(message, 'success', 'fas fa-check-circle');
    }
    
    static showErrorNotification(message) {
        this.createNotification(message, 'error', 'fas fa-exclamation-circle');
    }
    
    static showInfoNotification(message) {
        this.createNotification(message, 'info', 'fas fa-info-circle');
    }
    
    static showWarningNotification(message) {
        this.createNotification(message, 'warning', 'fas fa-exclamation-triangle');
    }
    
    static createNotification(message, type, icon) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
            <button class="notification-close">×</button>
        `;
        
        // إضافة الإشعار للصفحة
        document.body.appendChild(notification);
        
        // حركة الظهور
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);
        
        // إزالة عند النقر على زر الإغلاق
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }
    
    static hideNotification(notification) {
        notification.classList.add('hide');
        
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
}

// تهيئة جميع الحركات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    InteractiveEffects.addHoverEffects();
    InteractiveEffects.addScrollAnimations();
    
    // إضافة CSS للحركات المخصصة
    const style = document.createElement('style');
    style.textContent = `
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.hide {
            transform: translateX(100%);
        }
        
        .notification-success {
            border-left: 4px solid #28a745;
            color: #28a745;
        }
        
        .notification-error {
            border-left: 4px solid #dc3545;
            color: #dc3545;
        }
        
        .notification-info {
            border-left: 4px solid #17a2b8;
            color: #17a2b8;
        }
        
        .notification-warning {
            border-left: 4px solid #ffc107;
            color: #ffc107;
        }
        
        .notification-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            margin-right: -0.5rem;
        }
    `;
    document.head.appendChild(style);
});

// تصدير الكلاسات للاستخدام العام
window.SplashAnimations = SplashAnimations;
window.InterfaceAnimations = InterfaceAnimations;
window.InteractiveEffects = InteractiveEffects;
window.DataAnimations = DataAnimations;
window.NotificationAnimations = NotificationAnimations;
