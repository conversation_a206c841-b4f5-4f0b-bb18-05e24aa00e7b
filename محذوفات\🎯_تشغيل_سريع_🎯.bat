@echo off
chcp 65001 >nul
title نظام إدارة بيانات الموظفين - علي عاجل خشّان المحنّة

REM تعيين ألوان جميلة
color 0A

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ║                                                                             ║
echo ║                    🚀 نظام إدارة بيانات الموظفين 🚀                      ║
echo ║                                                                             ║
echo ║                  👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻                ║
echo ║                                                                             ║
echo ║                  🏛️ محافظة الديوانية / شعبة الرواتب 🏛️                 ║
echo ║                                                                             ║
echo ║                📧 <EMAIL>  📱 07727232639                     ║
echo ║                                                                             ║
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🔍 فحص المتطلبات...

REM التحقق من Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت! يرجى تثبيته من: https://nodejs.org
    echo 📞 للدعم: <EMAIL> أو 07727232639
    pause
    exit /b 1
)
echo ✅ Node.js موجود

REM التحقق من ملفات المشروع
if not exist "package.json" (
    echo ❌ ملفات المشروع غير موجودة!
    echo 📞 للدعم: <EMAIL> أو 07727232639
    pause
    exit /b 1
)
echo ✅ ملفات المشروع موجودة

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" (
    mkdir database_safe
    echo ✅ تم إنشاء مجلد قاعدة البيانات
)

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات للمرة الأولى...
    echo ⏳ يرجى الانتظار (قد يستغرق دقائق قليلة)...
    echo.
    
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات!
        echo 🌐 تأكد من اتصالك بالإنترنت
        echo 📞 للدعم: <EMAIL> أو 07727232639
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح!
)

echo.
echo 🚀 تشغيل البرنامج...
echo 💡 ستفتح نافذة البرنامج خلال ثوانٍ قليلة
echo 🔄 لإغلاق البرنامج، أغلق نافذة البرنامج أو اضغط Ctrl+C هنا
echo.

REM تشغيل البرنامج
npm start

echo.
echo 📴 تم إغلاق البرنامج
echo 🙏 شكراً لاستخدام نظام إدارة بيانات الموظفين
echo 📞 للدعم: <EMAIL> أو 07727232639
echo.
pause
