// ملف اختبار البرنامج
const fs = require('fs');
const path = require('path');

console.log('🧪 اختبار نظام إدارة بيانات الموظفين');
console.log('المبرمج: علي عاجل خشّان المحنّة');
console.log('=====================================\n');

// اختبار وجود الملفات الأساسية
const requiredFiles = [
    'package.json',
    'main.js',
    'index.html',
    'styles/main.css',
    'styles/themes.css',
    'styles/animations.css',
    'scripts/main.js',
    'scripts/database.js',
    'scripts/themes.js',
    'scripts/animations.js'
];

console.log('📁 فحص الملفات الأساسية:');
let allFilesExist = true;

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - مفقود`);
        allFilesExist = false;
    }
});

console.log('\n📦 فحص package.json:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    console.log(`✅ اسم المشروع: ${packageJson.name}`);
    console.log(`✅ الإصدار: ${packageJson.version}`);
    console.log(`✅ المؤلف: ${packageJson.author}`);
    console.log(`✅ الملف الرئيسي: ${packageJson.main}`);
    
    // فحص التبعيات
    if (packageJson.dependencies) {
        console.log('✅ التبعيات موجودة:');
        Object.keys(packageJson.dependencies).forEach(dep => {
            console.log(`   - ${dep}: ${packageJson.dependencies[dep]}`);
        });
    }
    
    if (packageJson.devDependencies) {
        console.log('✅ تبعيات التطوير موجودة:');
        Object.keys(packageJson.devDependencies).forEach(dep => {
            console.log(`   - ${dep}: ${packageJson.devDependencies[dep]}`);
        });
    }
    
} catch (error) {
    console.log('❌ خطأ في قراءة package.json:', error.message);
    allFilesExist = false;
}

console.log('\n🗂️ فحص المجلدات:');
const requiredDirs = ['styles', 'scripts', 'assets'];

requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`✅ ${dir}/`);
    } else {
        console.log(`❌ ${dir}/ - مفقود`);
        allFilesExist = false;
    }
});

// فحص مجلد قاعدة البيانات
if (fs.existsSync('database_safe')) {
    console.log('✅ database_safe/ - موجود');
} else {
    console.log('⚠️ database_safe/ - سيتم إنشاؤه عند التشغيل');
}

console.log('\n📄 فحص ملفات HTML:');
try {
    const htmlContent = fs.readFileSync('index.html', 'utf8');
    
    // فحص العناصر الأساسية
    const requiredElements = [
        'splashScreen',
        'mainApp',
        'yearSelection',
        'tabsInterface',
        'employee-info',
        'reports',
        'comparison',
        'settings'
    ];
    
    requiredElements.forEach(elementId => {
        if (htmlContent.includes(`id="${elementId}"`)) {
            console.log(`✅ عنصر ${elementId} موجود`);
        } else {
            console.log(`❌ عنصر ${elementId} مفقود`);
            allFilesExist = false;
        }
    });
    
    // فحص ربط ملفات CSS و JS
    if (htmlContent.includes('styles/main.css')) {
        console.log('✅ ربط ملف CSS الرئيسي');
    } else {
        console.log('❌ ربط ملف CSS الرئيسي مفقود');
    }
    
    if (htmlContent.includes('scripts/main.js')) {
        console.log('✅ ربط ملف JavaScript الرئيسي');
    } else {
        console.log('❌ ربط ملف JavaScript الرئيسي مفقود');
    }
    
} catch (error) {
    console.log('❌ خطأ في قراءة index.html:', error.message);
    allFilesExist = false;
}

console.log('\n🎨 فحص ملفات CSS:');
try {
    const mainCss = fs.readFileSync('styles/main.css', 'utf8');
    const themesCss = fs.readFileSync('styles/themes.css', 'utf8');
    
    // فحص الكلاسات الأساسية
    const requiredClasses = [
        '.splash-screen',
        '.main-app',
        '.year-selection-container',
        '.tabs-interface',
        '.tab-content',
        '.btn'
    ];
    
    requiredClasses.forEach(className => {
        if (mainCss.includes(className)) {
            console.log(`✅ كلاس ${className} موجود`);
        } else {
            console.log(`❌ كلاس ${className} مفقود`);
        }
    });
    
    // فحص الثيمات
    const themes = ['volcano', 'violet-blue', 'silver-yellow', 'sky-blue', 'grey-cyan', 'grass-green', 'orange'];
    themes.forEach(theme => {
        if (themesCss.includes(`[data-theme="${theme}"]`)) {
            console.log(`✅ ثيم ${theme} موجود`);
        } else {
            console.log(`❌ ثيم ${theme} مفقود`);
        }
    });
    
} catch (error) {
    console.log('❌ خطأ في قراءة ملفات CSS:', error.message);
}

console.log('\n⚙️ فحص ملفات JavaScript:');
try {
    const mainJs = fs.readFileSync('scripts/main.js', 'utf8');
    
    // فحص الوظائف الأساسية
    const requiredFunctions = [
        'initializeSplashScreen',
        'loadYears',
        'saveEmployee',
        'loadReportData',
        'changeTheme'
    ];
    
    requiredFunctions.forEach(funcName => {
        if (mainJs.includes(`function ${funcName}`) || mainJs.includes(`${funcName} =`)) {
            console.log(`✅ وظيفة ${funcName} موجودة`);
        } else {
            console.log(`❌ وظيفة ${funcName} مفقودة`);
        }
    });
    
} catch (error) {
    console.log('❌ خطأ في قراءة ملفات JavaScript:', error.message);
}

console.log('\n📊 ملخص الاختبار:');
if (allFilesExist) {
    console.log('🎉 جميع الملفات الأساسية موجودة!');
    console.log('✅ البرنامج جاهز للتشغيل');
    console.log('\n🚀 لتشغيل البرنامج:');
    console.log('   npm install  (للمرة الأولى)');
    console.log('   npm start');
    console.log('\n📝 أو استخدم:');
    console.log('   start.bat     (للمستخدمين)');
    console.log('   dev-start.bat (للمطورين)');
} else {
    console.log('⚠️ بعض الملفات مفقودة!');
    console.log('❌ يرجى التأكد من وجود جميع الملفات قبل التشغيل');
}

console.log('\n=====================================');
console.log('تم الانتهاء من الاختبار');
console.log('المبرمج: علي عاجل خشّان المحنّة');
console.log('© 2025 - جميع الحقوق محفوظة');
