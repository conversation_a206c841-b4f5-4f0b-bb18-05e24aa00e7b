@echo off
chcp 65001 >nul
title نظام إدارة بيانات الموظفين - وضع التطوير

echo.
echo ================================================
echo    نظام إدارة بيانات الموظفين - وضع التطوير
echo    المبرمج: علي عاجل خشّان المحنّة
echo ================================================
echo.

echo تشغيل البرنامج في وضع التطوير...
echo سيتم فتح أدوات المطور تلقائياً
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من الموقع الرسمي: https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo جاري تثبيت التبعيات...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" (
    mkdir database_safe
)

REM تشغيل البرنامج
npm start

pause
