@echo off
chcp 65001 >nul
title نظام إدارة بيانات الموظفين - علي عاجل خشّان المحنّة

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ║                                                                             ║
echo ║                    🚀 نظام إدارة بيانات الموظفين 🚀                      ║
echo ║                                                                             ║
echo ║                  👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻                ║
echo ║                                                                             ║
echo ║                  🏛️ محافظة الديوانية / شعبة الرواتب 🏛️                 ║
echo ║                                                                             ║
echo ║                📧 <EMAIL>  📱 07727232639                     ║
echo ║                                                                             ║
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🔍 فحص المتطلبات...

REM التحقق من Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js من الرابط التالي:
    echo    https://nodejs.org
    echo.
    echo 📞 للدعم الفني:
    echo    📧 البريد الإلكتروني: <EMAIL>
    echo    📱 الهاتف/واتساب: 07727232639
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)
echo ✅ Node.js موجود

REM التحقق من ملفات المشروع
if not exist "package.json" (
    echo.
    echo ❌ ملفات المشروع غير موجودة!
    echo.
    echo 📁 تأكد من وضع ملف التشغيل في نفس مجلد البرنامج
    echo    المجلد يجب أن يحتوي على:
    echo    • package.json
    echo    • main.js
    echo    • index.html
    echo    • مجلد scripts
    echo    • مجلد styles
    echo.
    echo 📞 للدعم الفني:
    echo    📧 البريد الإلكتروني: <EMAIL>
    echo    📱 الهاتف/واتساب: 07727232639
    echo.
    echo اضغط أي زر للخروج...
    pause >nul
    exit /b 1
)
echo ✅ ملفات المشروع موجودة

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" (
    mkdir database_safe
    echo ✅ تم إنشاء مجلد قاعدة البيانات
) else (
    echo ✅ مجلد قاعدة البيانات موجود
)

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات للمرة الأولى...
    echo ⏳ هذا قد يستغرق عدة دقائق، يرجى الانتظار...
    echo 🌐 تأكد من اتصالك بالإنترنت
    echo.
    
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ❌ فشل في تثبيت التبعيات!
        echo.
        echo 🔧 الحلول المقترحة:
        echo    1. تأكد من اتصالك بالإنترنت
        echo    2. أعد تشغيل البرنامج كمدير (Run as Administrator)
        echo    3. تأكد من عدم وجود برامج حماية تمنع التثبيت
        echo    4. جرب إغلاق برامج مكافحة الفيروسات مؤقتاً
        echo.
        echo 📞 للدعم الفني:
        echo    📧 البريد الإلكتروني: <EMAIL>
        echo    📱 الهاتف/واتساب: 07727232639
        echo.
        echo اضغط أي زر للخروج...
        pause >nul
        exit /b 1
    )
    echo.
    echo ✅ تم تثبيت التبعيات بنجاح!
) else (
    echo ✅ التبعيات مثبتة مسبقاً
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ║                          🚀 تشغيل البرنامج 🚀                            ║
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🎉 جميع المتطلبات متوفرة!
echo 🚀 جاري تشغيل البرنامج...
echo.
echo 💡 ملاحظات مهمة:
echo    • ستفتح نافذة البرنامج خلال ثوانٍ قليلة
echo    • لا تغلق هذه النافذة أثناء عمل البرنامج
echo    • لإغلاق البرنامج، أغلق نافذة البرنامج أو اضغط Ctrl+C هنا
echo    • إذا ظهرت رسائل خطأ تقنية، تجاهلها إذا كان البرنامج يعمل
echo.
echo ═══════════════════════════════════════════════════════════════════════════════

REM تشغيل البرنامج
npm start

REM إذا وصل هنا فقد تم إغلاق البرنامج
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo ║                          📴 تم إغلاق البرنامج                            ║
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🙏 شكراً لاستخدام نظام إدارة بيانات الموظفين
echo.
echo 📞 للدعم الفني أو الاستفسارات:
echo    👨‍💻 المبرمج: علي عاجل خشّان المحنّة
echo    🏛️ الجهة: محافظة الديوانية / شعبة الرواتب
echo    📧 البريد الإلكتروني: <EMAIL>
echo    📱 الهاتف/واتساب: 07727232639
echo.
echo اضغط أي زر للخروج...
pause >nul
