{"name": "employee-management-system", "version": "1.0.0", "description": "نظام إدارة بيانات الموظفين - المبرمج: علي عاجل خشّان المحنّة", "main": "main.js", "scripts": {"start": "electron . --no-sandbox", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "author": {"name": "علي عاجل خشّان المحنّة", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"sqlite3": "^5.1.6"}, "build": {"appId": "com.aliajil.employee-management", "productName": "نظام إدارة بيانات الموظفين", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/sqlite3/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}