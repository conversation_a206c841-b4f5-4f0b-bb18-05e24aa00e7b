@echo off
chcp 65001 >nul

echo نظام إدارة بيانات الموظفين
echo المبرمج: علي عاجل خشّان المحنّة
echo محافظة الديوانية / شعبة الرواتب
echo.

REM التحقق من Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من ملفات المشروع
if not exist "package.json" (
    echo خطأ: ملفات المشروع غير موجودة
    echo تأكد من وضع الملف في مجلد البرنامج الصحيح
    pause
    exit /b 1
)

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" mkdir database_safe

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo جاري تثبيت التبعيات...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo فشل في تثبيت التبعيات
        echo تأكد من اتصالك بالإنترنت
        pause
        exit /b 1
    )
)

echo.
echo جاري تشغيل البرنامج...
npm start

pause
