<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- تدرج لوني للخلفية -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b0000;stop-opacity:1" />
    </linearGradient>
    
    <!-- تدرج لوني للأيقونات -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffc107;stop-opacity:1" />
    </linearGradient>
    
    <!-- ظل للنص -->
    <filter id="textShadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- توهج للأيقونات -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- الخلفية الدائرية -->
  <circle cx="128" cy="128" r="120" fill="url(#backgroundGradient)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- دائرة داخلية للتأثير -->
  <circle cx="128" cy="128" r="100" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  
  <!-- أيقونة المبنى الحكومي -->
  <g transform="translate(128, 80)">
    <!-- القاعدة -->
    <rect x="-40" y="30" width="80" height="8" fill="url(#iconGradient)" filter="url(#glow)"/>
    
    <!-- الأعمدة -->
    <rect x="-35" y="10" width="8" height="20" fill="url(#iconGradient)"/>
    <rect x="-20" y="10" width="8" height="20" fill="url(#iconGradient)"/>
    <rect x="-5" y="10" width="8" height="20" fill="url(#iconGradient)"/>
    <rect x="10" y="10" width="8" height="20" fill="url(#iconGradient)"/>
    <rect x="25" y="10" width="8" height="20" fill="url(#iconGradient)"/>
    
    <!-- السقف -->
    <polygon points="-45,10 0,-10 45,10" fill="url(#iconGradient)" filter="url(#glow)"/>
    
    <!-- النجمة في الوسط -->
    <polygon points="0,-5 2,0 7,0 3,3 5,8 0,5 -5,8 -3,3 -7,0 -2,0" fill="#ffffff" filter="url(#glow)"/>
  </g>
  
  <!-- أيقونة الأشخاص -->
  <g transform="translate(128, 160)">
    <!-- الشخص الأول -->
    <g transform="translate(-20, 0)">
      <circle cx="0" cy="-10" r="6" fill="url(#iconGradient)"/>
      <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient)"/>
    </g>
    
    <!-- الشخص الثاني -->
    <g transform="translate(0, 0)">
      <circle cx="0" cy="-10" r="6" fill="url(#iconGradient)"/>
      <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient)"/>
    </g>
    
    <!-- الشخص الثالث -->
    <g transform="translate(20, 0)">
      <circle cx="0" cy="-10" r="6" fill="url(#iconGradient)"/>
      <rect x="-4" y="-4" width="8" height="12" rx="2" fill="url(#iconGradient)"/>
    </g>
  </g>
  
  <!-- أيقونة الملف/التقرير -->
  <g transform="translate(80, 120)">
    <rect x="0" y="0" width="16" height="20" rx="2" fill="url(#iconGradient)" filter="url(#glow)"/>
    <rect x="2" y="4" width="12" height="2" fill="#ffffff"/>
    <rect x="2" y="8" width="12" height="2" fill="#ffffff"/>
    <rect x="2" y="12" width="8" height="2" fill="#ffffff"/>
  </g>
  
  <!-- أيقونة الإعدادات -->
  <g transform="translate(160, 120)">
    <circle cx="0" cy="0" r="8" fill="none" stroke="url(#iconGradient)" stroke-width="3" filter="url(#glow)"/>
    <circle cx="0" cy="0" r="3" fill="url(#iconGradient)"/>
    <rect x="-1" y="-12" width="2" height="4" fill="url(#iconGradient)"/>
    <rect x="-1" y="8" width="2" height="4" fill="url(#iconGradient)"/>
    <rect x="8" y="-1" width="4" height="2" fill="url(#iconGradient)"/>
    <rect x="-12" y="-1" width="4" height="2" fill="url(#iconGradient)"/>
  </g>
  
  <!-- النص العربي -->
  <text x="128" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ffffff" filter="url(#textShadow)">
    إدارة الموظفين
  </text>
  
  <!-- خط زخرفي -->
  <path d="M 50 200 Q 128 190 206 200" stroke="url(#iconGradient)" stroke-width="2" fill="none" filter="url(#glow)"/>
</svg>
