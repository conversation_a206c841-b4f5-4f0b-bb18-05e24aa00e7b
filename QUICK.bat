@echo off

REM إخفاء النافذة نهائياً من البداية
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

REM تثبيت التبعيات بصمت إذا لم تكن مثبتة
if not exist "node_modules" npm install >nul 2>nul

REM تشغيل البرنامج مع إخفاء كامل للنوافذ
powershell -WindowStyle Hidden -Command "Start-Process -FilePath 'npm' -ArgumentList 'start' -WindowStyle Hidden -CreateNoWindow"

REM إنهاء النافذة الحالية فوراً
exit
