// الملف الرئيسي للتطبيق
const { ipc<PERSON>ender<PERSON> } = require('electron');

// متغيرات عامة
let currentYear = null;
let currentTheme = 'volcano';

// عناصر DOM
const splashScreen = document.getElementById('splashScreen');
const mainApp = document.getElementById('mainApp');
const yearSelection = document.getElementById('yearSelection');
const tabsInterface = document.getElementById('tabsInterface');

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeSplashScreen();
    setupEventListeners();
    loadSavedTheme();
});

// تهيئة شاشة البداية
function initializeSplashScreen() {
    const appTitle = document.getElementById('appTitle');
    const programmerName = document.getElementById('programmerName');
    const organizationName = document.getElementById('organizationName');
    const programmerNameDisplay = document.getElementById('programmerNameDisplay');

    // عرض عنوان التطبيق حرف بحرف
    const titleText = 'نظام إدارة بيانات الموظفين';
    typeWriter(appTitle, titleText, 100);

    // عرض اسم المبرمج بدلاً من الأيقونة
    setTimeout(() => {
        const programmerDisplayText = 'المبرمج: علي عاجل خشّان المحنّة';
        typeWriter(programmerNameDisplay, programmerDisplayText, 80);
    }, 1500);

    // عرض اسم المبرمج في المكان الأصلي
    setTimeout(() => {
        const programmerText = 'المبرمج: علي عاجل خشّان المحنّة';
        createFallingLetters(programmerName, programmerText);
    }, 3000);

    // عرض معلومات الجهة
    setTimeout(() => {
        const organizationText = 'محافظة الديوانية / شعبة الرواتب';
        typeWriter(organizationName, organizationText, 80);
        organizationName.parentElement.classList.add('fade-in');
    }, 4500);

    // الانتقال للواجهة الرئيسية
    setTimeout(() => {
        hideSplashScreen();
    }, 7000);
}

// كتابة النص حرف بحرف
function typeWriter(element, text, speed) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// إنشاء أحرف متساقطة
function createFallingLetters(element, text) {
    element.innerHTML = '';
    
    for (let i = 0; i < text.length; i++) {
        const span = document.createElement('span');
        span.textContent = text[i] === ' ' ? '\u00A0' : text[i];
        span.className = 'letter-drop';
        span.style.animationDelay = `${i * 0.1}s`;
        element.appendChild(span);
    }
}

// إخفاء شاشة البداية
function hideSplashScreen() {
    splashScreen.classList.add('fade-out');
    setTimeout(() => {
        splashScreen.style.display = 'none';
        // إظهار التطبيق الرئيسي مباشرة بدون شاشة سوداء
        mainApp.classList.remove('hidden');
        mainApp.style.opacity = '0';
        mainApp.style.display = 'block';

        // تطبيق حركة الظهور التدريجي
        setTimeout(() => {
            mainApp.style.transition = 'opacity 0.8s ease-in-out';
            mainApp.style.opacity = '1';
            loadYears();
        }, 50);
    }, 500);
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار اختيار السنة
    document.getElementById('dropdownBtn').addEventListener('click', toggleYearDropdown);
    document.getElementById('addYearBtn').addEventListener('click', addNewYear);
    document.getElementById('enterYearBtn').addEventListener('click', enterYear);
    
    // التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', switchTab);
    });
    
    // زر العودة للقائمة الرئيسية
    document.getElementById('backToMainBtn').addEventListener('click', backToMain);
    
    // نموذج الموظف
    document.getElementById('employeeForm').addEventListener('submit', saveEmployee);
    document.getElementById('childrenCount').addEventListener('change', updateChildrenFields);
    
    // أزرار الثيمات
    document.querySelectorAll('.theme-btn').forEach(btn => {
        btn.addEventListener('click', changeTheme);
    });

    // أزرار قاعدة البيانات
    document.getElementById('exportDataBtn').addEventListener('click', exportData);
    document.getElementById('clearDataBtn').addEventListener('click', clearData);

    // أزرار البحث
    document.getElementById('searchNameBtn').addEventListener('click', searchByName);
    document.getElementById('clearSearchBtn').addEventListener('click', clearSearch);
    document.getElementById('applyFilterBtn').addEventListener('click', applyFilter);
    document.getElementById('applyResidenceFilterBtn').addEventListener('click', applyResidenceFilter);
    document.getElementById('clearResidenceFilterBtn').addEventListener('click', clearResidenceFilter);
    document.getElementById('showNotProvidedBtn').addEventListener('click', showNotProvided);
    document.getElementById('showAllBtn').addEventListener('click', showAllEmployees);
    document.getElementById('exportSearchBtn').addEventListener('click', exportSearchResults);
    document.getElementById('printSearchBtn').addEventListener('click', printSearchResults);

    // البحث الفوري عند الكتابة في حقل مكان السكن
    document.getElementById('filterResidence').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            if (currentYear && allEmployees.length > 0) {
                applyResidenceFilter();
            }
        }, 500); // انتظار 500ms بعد التوقف عن الكتابة
    });

    // البحث عند الكتابة
    document.getElementById('searchByName').addEventListener('input', debounce(searchByName, 300));
    
    // إغلاق القائمة المنسدلة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.input-with-dropdown')) {
            document.getElementById('yearDropdown').classList.add('hidden');
        }
    });
}

// تبديل القائمة المنسدلة للسنوات
function toggleYearDropdown() {
    const dropdown = document.getElementById('yearDropdown');
    dropdown.classList.toggle('hidden');
}

// تحميل السنوات المحفوظة
async function loadYears() {
    try {
        const years = await ipcRenderer.invoke('get-years');
        const yearsList = document.getElementById('yearsList');
        yearsList.innerHTML = '';
        
        if (years.length === 0) {
            yearsList.innerHTML = '<div class="year-item">لا توجد سنوات محفوظة</div>';
        } else {
            years.forEach(yearData => {
                const yearItem = document.createElement('div');
                yearItem.className = 'year-item';
                yearItem.textContent = yearData.year;
                yearItem.addEventListener('click', () => selectYear(yearData.year));
                yearsList.appendChild(yearItem);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل السنوات:', error);
        showMessage('خطأ', 'حدث خطأ في تحميل السنوات المحفوظة');
    }
}

// اختيار سنة من القائمة
function selectYear(year) {
    document.getElementById('yearInput').value = year;
    document.getElementById('yearDropdown').classList.add('hidden');
}

// إضافة سنة جديدة
async function addNewYear() {
    const yearInput = document.getElementById('yearInput');
    const year = parseInt(yearInput.value);
    
    if (!year || year < 2000 || year > 2100) {
        showMessage('خطأ', 'يرجى إدخال سنة صحيحة (2000-2100)');
        return;
    }
    
    try {
        await ipcRenderer.invoke('add-year', year);
        showMessage('نجح', `تم إضافة السنة ${year} بنجاح`);
        loadYears();
    } catch (error) {
        console.error('خطأ في إضافة السنة:', error);
        showMessage('خطأ', 'حدث خطأ في إضافة السنة');
    }
}

// الدخول إلى السنة المحددة
function enterYear() {
    const yearInput = document.getElementById('yearInput');
    const year = parseInt(yearInput.value);
    
    if (!year || year < 2000 || year > 2100) {
        showMessage('خطأ', 'يرجى إدخال سنة صحيحة (2000-2100)');
        return;
    }
    
    currentYear = year;
    
    // تحديث عرض السنة في جميع التبويبات
    document.querySelectorAll('.year-display, #currentYear').forEach(el => {
        el.textContent = year;
    });
    
    // إخفاء اختيار السنة وإظهار التبويبات
    yearSelection.classList.add('hidden');
    tabsInterface.classList.remove('hidden');
    tabsInterface.classList.add('fade-in');
    
    // تفعيل تبويب المقارنة إذا كان هناك أكثر من سنة
    checkComparisonTab();
    
    // تحميل بيانات التقرير
    loadReportData();
}

// التبديل بين التبويبات
function switchTab(e) {
    const targetTab = e.target.closest('.tab-btn').dataset.tab;
    
    // إزالة الفئة النشطة من جميع التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // إضافة الفئة النشطة للتبويب المحدد
    e.target.closest('.tab-btn').classList.add('active');
    document.getElementById(targetTab).classList.add('active');
    
    // تحميل البيانات حسب التبويب
    switch(targetTab) {
        case 'reports':
            loadReportData();
            break;
        case 'search':
            loadSearchData();
            break;
        case 'comparison':
            loadComparisonData();
            break;
    }
}

// العودة للقائمة الرئيسية
function backToMain() {
    tabsInterface.classList.add('hidden');
    yearSelection.classList.remove('hidden');
    yearSelection.classList.add('fade-in');
    
    // إعادة تعيين النموذج
    document.getElementById('employeeForm').reset();
    updateChildrenFields();
    
    // العودة للتبويب الأول
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.querySelector('.tab-btn[data-tab="employee-info"]').classList.add('active');
    document.getElementById('employee-info').classList.add('active');
}

// تحديث حقول الأطفال
function updateChildrenFields() {
    const childrenCount = parseInt(document.getElementById('childrenCount').value);
    const childrenDetails = document.getElementById('childrenDetails');
    
    childrenDetails.innerHTML = '';
    
    for (let i = 1; i <= Math.min(childrenCount, 4); i++) {
        const childSection = createChildSection(i);
        childrenDetails.appendChild(childSection);
    }
    
    if (childrenCount > 4) {
        const warning = document.createElement('div');
        warning.className = 'warning-message';
        warning.innerHTML = '<i class="fas fa-exclamation-triangle"></i> يمكن إدخال تفاصيل 4 أطفال فقط';
        childrenDetails.appendChild(warning);
    }
}

// إنشاء قسم طفل
function createChildSection(index) {
    const section = document.createElement('div');
    section.className = 'child-section';
    section.innerHTML = `
        <h4><i class="fas fa-child"></i> الطفل ${index}</h4>
        <div class="child-fields">
            <div class="form-group">
                <label>اسم الطفل:</label>
                <input type="text" id="childName${index}" required>
            </div>
            <div class="form-group">
                <label>تاريخ الميلاد:</label>
                <div class="date-inputs">
                    <input type="number" id="childDay${index}" placeholder="يوم" min="1" max="31" required>
                    <input type="number" id="childMonth${index}" placeholder="شهر" min="1" max="12" required>
                    <input type="number" id="childYear${index}" placeholder="سنة" min="1990" max="2025" required>
                </div>
            </div>
            <div class="form-group">
                <label>المرحلة الدراسية:</label>
                <select id="childEducation${index}" required>
                    <option value="">اختر المرحلة</option>
                    <option value="طفل">طفل</option>
                    <option value="ابتدائية">ابتدائية</option>
                    <option value="متوسطة">متوسطة</option>
                    <option value="إعدادية">إعدادية</option>
                    <option value="طالب جامعة">طالب جامعة</option>
                </select>
            </div>
            <div class="form-group">
                <label>الحالة:</label>
                <input type="text" id="childStatus${index}" readonly class="status-field">
            </div>
        </div>
    `;
    
    // إضافة مستمعي الأحداث للتحقق من العمر
    const dayInput = section.querySelector(`#childDay${index}`);
    const monthInput = section.querySelector(`#childMonth${index}`);
    const yearInput = section.querySelector(`#childYear${index}`);
    const educationSelect = section.querySelector(`#childEducation${index}`);
    
    [dayInput, monthInput, yearInput, educationSelect].forEach(input => {
        input.addEventListener('change', () => validateChildAge(index));
    });
    
    return section;
}

// التحقق من عمر الطفل
function validateChildAge(index) {
    const day = parseInt(document.getElementById(`childDay${index}`).value);
    const month = parseInt(document.getElementById(`childMonth${index}`).value);
    const year = parseInt(document.getElementById(`childYear${index}`).value);
    const education = document.getElementById(`childEducation${index}`).value;
    const statusField = document.getElementById(`childStatus${index}`);
    
    if (!day || !month || !year || !education) {
        statusField.value = '';
        statusField.className = 'status-field';
        return;
    }
    
    const birthDate = new Date(year, month - 1, day);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    let actualAge = age;
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        actualAge--;
    }
    
    let status = 'مسموح';
    let statusClass = 'status-allowed';
    
    // قواعد التحقق
    if (education === 'ابتدائية' && actualAge >= 18) {
        status = 'يرفع الابن';
        statusClass = 'status-rejected';
    } else if (education === 'متوسطة' || education === 'إعدادية') {
        if (actualAge > 18 || (actualAge === 20 && education === 'متوسطة')) {
            status = 'يرفع الابن';
            statusClass = 'status-rejected';
        }
    } else if (education === 'طالب جامعة' && actualAge >= 24) {
        status = 'يرفع الابن';
        statusClass = 'status-rejected';
    }
    
    statusField.value = status;
    statusField.className = `status-field ${statusClass}`;
}

// حفظ بيانات الموظف
async function saveEmployee(e) {
    e.preventDefault();

    const form = e.target;
    const editingId = form.dataset.editingId;

    const employeeData = {
        year: currentYear,
        name: document.getElementById('employeeName').value,
        spouse_name: document.getElementById('spouseName').value,
        spouse_status: getSpouseStatus(),
        spouse_other: document.getElementById('spouseOther').value,
        spouse_residence: document.getElementById('spouseResidence')?.value || '',
        benefit_status: document.getElementById('benefitStatus')?.value || '',
        not_provided_transaction: document.getElementById('notProvidedTransaction')?.checked || false,
        children: getChildrenData()
    };

    // التحقق من صحة البيانات
    if (!employeeData.name.trim()) {
        showMessage('خطأ', 'يرجى إدخال اسم الموظف');
        return;
    }

    try {
        let result;
        if (editingId) {
            // تحديث موظف موجود
            result = await ipcRenderer.invoke('update-employee', parseInt(editingId), employeeData);
            showMessage('نجح', 'تم تحديث بيانات الموظف بنجاح');
        } else {
            // إضافة موظف جديد
            result = await ipcRenderer.invoke('save-employee', employeeData);
            showMessage('نجح', 'تم حفظ بيانات الموظف بنجاح');
        }

        // إعادة تعيين النموذج
        form.reset();
        form.removeAttribute('data-editing-id');
        updateChildrenFields();
        loadReportData();

    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showMessage('خطأ', 'حدث خطأ في حفظ البيانات');
    }
}

// الحصول على حالة الزوج/الزوجة
function getSpouseStatus() {
    const employeeCheckbox = document.getElementById('spouseEmployee');
    const notEmployeeCheckbox = document.getElementById('spouseNotEmployee');
    
    if (employeeCheckbox.checked) return 'موظف';
    if (notEmployeeCheckbox.checked) return 'غير موظف';
    return '';
}

// الحصول على بيانات الأطفال
function getChildrenData() {
    const childrenCount = parseInt(document.getElementById('childrenCount').value);
    const children = [];
    
    for (let i = 1; i <= Math.min(childrenCount, 4); i++) {
        const name = document.getElementById(`childName${i}`)?.value;
        const day = document.getElementById(`childDay${i}`)?.value;
        const month = document.getElementById(`childMonth${i}`)?.value;
        const year = document.getElementById(`childYear${i}`)?.value;
        const education = document.getElementById(`childEducation${i}`)?.value;
        const status = document.getElementById(`childStatus${i}`)?.value;
        
        if (name && day && month && year && education) {
            children.push({
                name,
                birth_day: parseInt(day),
                birth_month: parseInt(month),
                birth_year: parseInt(year),
                education_level: education,
                status
            });
        }
    }
    
    return children;
}

// تحميل بيانات التقرير
async function loadReportData() {
    if (!currentYear) return;

    try {
        const stats = await ipcRenderer.invoke('get-report-stats', currentYear);

        // تحديث الإحصائيات مع حركة العد
        animateCounter('totalEmployees', stats.total || 0);
        animateCounter('completeRecords', stats.complete || 0);
        animateCounter('incompleteRecords', stats.incomplete || 0);
        animateCounter('notProvided', stats.notProvided || 0);

        // تحميل جدول البيانات
        loadEmployeesTable();

    } catch (error) {
        console.error('خطأ في تحميل بيانات التقرير:', error);
    }
}

// حركة العد للأرقام
function animateCounter(elementId, targetValue) {
    const element = document.getElementById(elementId);
    const startValue = 0;
    const duration = 1000;
    const increment = targetValue / (duration / 16);
    let currentValue = startValue;

    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }
        element.textContent = Math.floor(currentValue);
    }, 16);
}

// تحميل جدول الموظفين
async function loadEmployeesTable() {
    try {
        const employees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        createEmployeesTable(employees);
    } catch (error) {
        console.error('خطأ في تحميل جدول الموظفين:', error);
    }
}

// إنشاء جدول الموظفين
function createEmployeesTable(employees) {
    const tableContainer = document.getElementById('dataTable');

    const columns = [
        { key: 'name', title: 'اسم الموظف', width: '150px' },
        { key: 'spouse_name', title: 'اسم الزوج/الزوجة', width: '150px' },
        { key: 'spouse_status', title: 'حالة الزوج/الزوجة', width: '120px' },
        { key: 'children_count', title: 'عدد الأطفال', width: '100px' },
        {
            key: 'children_status',
            title: 'حالة الأطفال',
            width: '120px',
            render: (value, row) => {
                const allowedCount = row.children.filter(child => child.status === 'مسموح').length;
                const rejectedCount = row.children.filter(child => child.status === 'يرفع الابن').length;

                if (rejectedCount > 0) {
                    return `<span style="color: #dc3545; font-weight: bold;">${rejectedCount} مرفوض</span>`;
                } else if (allowedCount > 0) {
                    return `<span style="color: #28a745; font-weight: bold;">${allowedCount} مسموح</span>`;
                } else {
                    return '<span style="color: #6c757d;">لا يوجد</span>';
                }
            }
        },
        {
            key: 'actions',
            title: 'الإجراءات',
            width: '120px',
            sortable: false,
            render: (value, row) => {
                return `
                    <div class="actions-column">
                        <button class="action-btn edit" onclick="editEmployee(${row.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteEmployee(${row.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            }
        }
    ];

    if (window.TableManager) {
        window.TableManager.createTable(employees, columns, 'dataTable');
    } else {
        // إنشاء جدول بسيط إذا لم يكن TableManager متاحاً
        createSimpleTable(employees, columns, tableContainer);
    }
}

// تحميل بيانات البحث
async function loadSearchData() {
    if (!currentYear) return;

    try {
        allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        filteredEmployees = allEmployees;
        updateSearchResults();
    } catch (error) {
        console.error('خطأ في تحميل بيانات البحث:', error);
        showMessage('خطأ', 'حدث خطأ في تحميل بيانات البحث');
    }
}

// تحميل بيانات المقارنة
async function loadComparisonData() {
    if (!currentYear) return;

    try {
        const comparisonData = await ipcRenderer.invoke('get-comparison-data', currentYear);
        // سيتم تنفيذ عرض البيانات لاحقاً
    } catch (error) {
        console.error('خطأ في تحميل بيانات المقارنة:', error);
    }
}

// فحص تبويب المقارنة
async function checkComparisonTab() {
    try {
        const years = await ipcRenderer.invoke('get-years');
        const comparisonTab = document.getElementById('comparisonTab');
        
        if (years.length > 1) {
            comparisonTab.style.display = 'flex';
        } else {
            comparisonTab.style.display = 'none';
        }
    } catch (error) {
        console.error('خطأ في فحص السنوات:', error);
    }
}

// تغيير الثيم
function changeTheme(e) {
    const theme = e.target.closest('.theme-btn').dataset.theme;
    
    // إزالة الفئة النشطة من جميع الثيمات
    document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
    
    // إضافة الفئة النشطة للثيم المحدد
    e.target.closest('.theme-btn').classList.add('active');
    
    // تطبيق الثيم
    document.body.setAttribute('data-theme', theme);
    currentTheme = theme;
    
    // حفظ الثيم
    localStorage.setItem('selectedTheme', theme);
    
    showMessage('نجح', `تم تطبيق ثيم ${e.target.closest('.theme-btn').querySelector('span').textContent} بنجاح`);
}

// تحميل الثيم المحفوظ
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('selectedTheme') || 'volcano';
    document.body.setAttribute('data-theme', savedTheme);
    currentTheme = savedTheme;
    
    // تحديد الثيم النشط
    document.querySelectorAll('.theme-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.theme === savedTheme) {
            btn.classList.add('active');
        }
    });
}

// تصدير البيانات
async function exportData() {
    showConfirm('تصدير البيانات', 'هل تريد تصدير جميع البيانات؟', async () => {
        try {
            const result = await ipcRenderer.invoke('backup-database');
            if (result.success) {
                showMessage('نجح', `تم تصدير البيانات بنجاح إلى: ${result.path}`);
            } else if (result.canceled) {
                showMessage('معلومات', 'تم إلغاء عملية التصدير');
            }
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            showMessage('خطأ', 'حدث خطأ في تصدير البيانات');
        }
    });
}

// تفريغ قاعدة البيانات
async function clearData() {
    showConfirm('تفريغ القاعدة', 'هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!', async () => {
        try {
            await ipcRenderer.invoke('clear-database');
            showMessage('نجح', 'تم تفريغ قاعدة البيانات بنجاح');
            loadYears();

            // العودة لاختيار السنة إذا كنا في التبويبات
            if (!tabsInterface.classList.contains('hidden')) {
                backToMain();
            }
        } catch (error) {
            console.error('خطأ في تفريغ قاعدة البيانات:', error);
            showMessage('خطأ', 'حدث خطأ في تفريغ قاعدة البيانات');
        }
    });
}

// عرض رسالة
function showMessage(title, message) {
    document.getElementById('messageTitle').textContent = title;
    document.getElementById('messageText').textContent = message;
    document.getElementById('messageModal').classList.remove('hidden');
    document.getElementById('messageModal').classList.add('show');
}

// عرض تأكيد
function showConfirm(title, message, callback) {
    document.getElementById('confirmTitle').textContent = title;
    document.getElementById('confirmMessage').textContent = message;
    document.getElementById('confirmModal').classList.remove('hidden');
    document.getElementById('confirmModal').classList.add('show');
    
    document.getElementById('confirmYes').onclick = () => {
        hideModal('confirmModal');
        callback();
    };
}

// إخفاء النافذة المنبثقة
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('hide');
    setTimeout(() => {
        modal.classList.remove('show', 'hide');
        modal.classList.add('hidden');
    }, 300);
}

// إعداد أحداث النوافذ المنبثقة
document.getElementById('messageOk').addEventListener('click', () => hideModal('messageModal'));
document.getElementById('confirmNo').addEventListener('click', () => hideModal('confirmModal'));

// إعداد أحداث حالة الزوج/الزوجة
document.getElementById('spouseEmployee').addEventListener('change', function() {
    if (this.checked) {
        document.getElementById('spouseNotEmployee').checked = false;
        document.getElementById('benefitGroup').style.display = 'block';
    } else {
        document.getElementById('benefitGroup').style.display = 'none';
    }
});

document.getElementById('spouseNotEmployee').addEventListener('change', function() {
    if (this.checked) {
        document.getElementById('spouseEmployee').checked = false;
        document.getElementById('benefitGroup').style.display = 'none';
    }
});

// إنشاء جدول بسيط
function createSimpleTable(data, columns, container) {
    const table = document.createElement('table');
    table.className = 'data-table';

    // رأس الجدول
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    columns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column.title;
        if (column.width) th.style.width = column.width;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // جسم الجدول
    const tbody = document.createElement('tbody');

    if (data.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = columns.length;
        emptyCell.textContent = 'لا توجد بيانات';
        emptyCell.style.textAlign = 'center';
        emptyCell.style.padding = '2rem';
        emptyRow.appendChild(emptyCell);
        tbody.appendChild(emptyRow);
    } else {
        data.forEach(row => {
            const tr = document.createElement('tr');

            columns.forEach(column => {
                const td = document.createElement('td');

                if (column.render) {
                    td.innerHTML = column.render(row[column.key], row);
                } else {
                    td.textContent = row[column.key] || '';
                }

                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });
    }

    table.appendChild(tbody);
    container.innerHTML = '';
    container.appendChild(table);
}

// تعديل موظف
async function editEmployee(employeeId) {
    try {
        const employees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        const employee = employees.find(emp => emp.id === employeeId);

        if (employee) {
            // ملء النموذج ببيانات الموظف
            document.getElementById('employeeName').value = employee.name || '';
            document.getElementById('spouseName').value = employee.spouse_name || '';

            // تحديد حالة الزوج/الزوجة
            if (employee.spouse_status === 'موظف') {
                document.getElementById('spouseEmployee').checked = true;
                document.getElementById('benefitGroup').style.display = 'block';
            } else if (employee.spouse_status === 'غير موظف') {
                document.getElementById('spouseNotEmployee').checked = true;
            }

            document.getElementById('spouseOther').value = employee.spouse_other || '';

            // ملء الحقول الإضافية
            if (document.getElementById('spouseResidence')) {
                document.getElementById('spouseResidence').value = employee.spouse_residence || '';
            }
            if (document.getElementById('benefitStatus')) {
                document.getElementById('benefitStatus').value = employee.benefit_status || '';
            }
            if (document.getElementById('notProvidedTransaction')) {
                document.getElementById('notProvidedTransaction').checked = employee.not_provided_transaction || false;
            }

            document.getElementById('childrenCount').value = employee.children_count || 0;

            // تحديث حقول الأطفال
            updateChildrenFields();

            // ملء بيانات الأطفال
            employee.children.forEach((child, index) => {
                if (index < 4) {
                    const childIndex = index + 1;
                    document.getElementById(`childName${childIndex}`).value = child.name || '';
                    document.getElementById(`childDay${childIndex}`).value = child.birth_day || '';
                    document.getElementById(`childMonth${childIndex}`).value = child.birth_month || '';
                    document.getElementById(`childYear${childIndex}`).value = child.birth_year || '';
                    document.getElementById(`childEducation${childIndex}`).value = child.education_level || '';

                    // التحقق من العمر
                    validateChildAge(childIndex);
                }
            });

            // التبديل لتبويب معلومات الموظف
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelector('.tab-btn[data-tab="employee-info"]').classList.add('active');
            document.getElementById('employee-info').classList.add('active');

            // حفظ معرف الموظف للتحديث
            document.getElementById('employeeForm').dataset.editingId = employeeId;

            showMessage('معلومات', 'تم تحميل بيانات الموظف للتعديل');
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الموظف:', error);
        showMessage('خطأ', 'حدث خطأ في تحميل بيانات الموظف');
    }
}

// حذف موظف
async function deleteEmployee(employeeId) {
    showConfirm('حذف موظف', 'هل أنت متأكد من حذف هذا الموظف؟ لا يمكن التراجع عن هذا الإجراء!', async () => {
        try {
            await ipcRenderer.invoke('delete-employee', employeeId);
            showMessage('نجح', 'تم حذف الموظف بنجاح');
            loadReportData(); // إعادة تحميل البيانات
        } catch (error) {
            console.error('خطأ في حذف الموظف:', error);
            showMessage('خطأ', 'حدث خطأ في حذف الموظف');
        }
    });
}

// إعداد أحداث التقرير
document.getElementById('filterDataBtn').addEventListener('click', loadEmployeesTable);
document.getElementById('exportExcelBtn').addEventListener('click', exportToExcel);
document.getElementById('printReportBtn').addEventListener('click', printReport);

// تصدير إلى Excel
async function exportToExcel() {
    try {
        const employees = await ipcRenderer.invoke('get-employees-by-year', currentYear);

        // إنشاء البيانات للتصدير
        const exportData = employees.map((employee, index) => {
            const allowedChildren = employee.children.filter(child => child.status === 'مسموح').length;
            const rejectedChildren = employee.children.filter(child => child.status === 'يرفع الابن').length;

            let childrenStatus = 'لا يوجد';
            if (rejectedChildren > 0) {
                childrenStatus = `${rejectedChildren} مرفوض`;
            } else if (allowedChildren > 0) {
                childrenStatus = `${allowedChildren} مسموح`;
            }

            let generalStatus = 'مكتمل';

            // فحص خانة "لم يزودنا بمعاملة" أولاً
            if (employee.not_provided_transaction) {
                generalStatus = 'غير مزود';
            } else {
                const hasRejectedChildren = employee.children.some(child => child.status === 'يرفع الابن');
                const hasIncompleteData = !employee.spouse_name || !employee.spouse_status;
                const isNotProvidedOld = employee.spouse_other && employee.spouse_other.includes('لم يزودنا');

                if (isNotProvidedOld || hasRejectedChildren || hasIncompleteData) {
                    generalStatus = 'ناقص';
                }
            }

            return {
                'ت': index + 1,
                'اسم الموظف': employee.name || '',
                'اسم الزوج/الزوجة': employee.spouse_name || '',
                'حالة الزوج/الزوجة': employee.spouse_status || '',
                'مكان السكن': employee.spouse_residence || '',
                'عدم الاستفادة': employee.benefit_status || '',
                'لم يزود المعاملة': employee.not_provided_transaction ? 'نعم' : 'لا',
                'عدد الأطفال': employee.children_count || 0,
                'حالة الأطفال': childrenStatus,
                'الحالة العامة': generalStatus,
                'ملاحظات': employee.spouse_other || ''
            };
        });

        // تصدير البيانات
        await exportToExcelFile(exportData, `تقرير_الموظفين_${currentYear}`);

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showMessage('خطأ', 'حدث خطأ في تصدير البيانات');
    }
}

// طباعة التقرير
function printReport() {
    try {
        printReportWithPreview('dataTable', `تقرير الموظفين - السنة ${currentYear}`);
    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        showMessage('خطأ', 'حدث خطأ في طباعة التقرير');
    }
}

// وظيفة الخروج من التطبيق
function exitApplication() {
    showConfirm('خروج من البرنامج', 'هل أنت متأكد من الخروج من البرنامج؟', () => {
        // إغلاق التطبيق في Electron
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            ipcRenderer.send('app-quit');
        } else {
            // في المتصفح العادي
            window.close();
        }
    });
}

// متغيرات البحث
let allEmployees = [];
let filteredEmployees = [];

// البحث بالاسم
async function searchByName() {
    const searchTerm = document.getElementById('searchByName').value.trim();

    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        if (!allEmployees.length) {
            allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        }

        if (searchTerm === '') {
            filteredEmployees = allEmployees;
        } else {
            filteredEmployees = allEmployees.filter(employee =>
                employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (employee.spouse_name && employee.spouse_name.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        updateSearchResults();

    } catch (error) {
        console.error('خطأ في البحث:', error);
        showMessage('خطأ', 'حدث خطأ في البحث');
    }
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchByName').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterResidence').value = '';
    filteredEmployees = allEmployees;
    updateSearchResults();
}

// تطبيق التصفية
async function applyFilter() {
    const filterValue = document.getElementById('filterStatus').value;

    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        if (!allEmployees.length) {
            allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        }

        switch (filterValue) {
            case 'incomplete':
                filteredEmployees = allEmployees.filter(employee => {
                    const hasRejectedChildren = employee.children.some(child => child.status === 'يرفع الابن');
                    const hasIncompleteData = !employee.spouse_name || !employee.spouse_status;
                    return hasRejectedChildren || hasIncompleteData;
                });
                break;

            case 'complete':
                filteredEmployees = allEmployees.filter(employee => {
                    const allChildrenAllowed = employee.children.every(child => child.status === 'مسموح');
                    const hasCompleteData = employee.spouse_name && employee.spouse_status;
                    return allChildrenAllowed && hasCompleteData;
                });
                break;

            case 'not-provided':
                filteredEmployees = allEmployees.filter(employee =>
                    employee.not_provided_transaction ||
                    (employee.spouse_other && employee.spouse_other.includes('لم يزودنا'))
                );
                break;

            default:
                filteredEmployees = allEmployees;
        }

        updateSearchResults();

    } catch (error) {
        console.error('خطأ في التصفية:', error);
        showMessage('خطأ', 'حدث خطأ في التصفية');
    }
}

// تطبيق التصفية حسب مكان السكن
async function applyResidenceFilter() {
    const residenceValue = document.getElementById('filterResidence').value.trim();

    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        if (!allEmployees.length) {
            allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        }

        if (residenceValue === '') {
            filteredEmployees = allEmployees;
            showMessage('معلومات', 'يتم عرض جميع الموظفين');
        } else {
            // البحث الجزئي - يبحث عن النص في أي مكان من حقل مكان السكن
            filteredEmployees = allEmployees.filter(employee => {
                if (!employee.spouse_residence) return false;
                return employee.spouse_residence.toLowerCase().includes(residenceValue.toLowerCase());
            });

            if (filteredEmployees.length === 0) {
                showMessage('تنبيه', `لم يتم العثور على موظفين في "${residenceValue}"`);
            } else {
                showMessage('نجح', `تم العثور على ${filteredEmployees.length} موظف في "${residenceValue}"`);
            }
        }

        updateSearchResults();

    } catch (error) {
        console.error('خطأ في التصفية حسب السكن:', error);
        showMessage('خطأ', 'حدث خطأ في التصفية حسب مكان السكن');
    }
}

// مسح البحث حسب مكان السكن
async function clearResidenceFilter() {
    document.getElementById('filterResidence').value = '';

    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        if (!allEmployees.length) {
            allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        }

        filteredEmployees = allEmployees;
        updateSearchResults();
        showMessage('معلومات', 'تم مسح البحث وعرض جميع الموظفين');

    } catch (error) {
        console.error('خطأ في مسح البحث:', error);
        showMessage('خطأ', 'حدث خطأ في مسح البحث');
    }
}

// عرض الغير مزودين
async function showNotProvided() {
    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        if (!allEmployees.length) {
            allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        }

        filteredEmployees = allEmployees.filter(employee =>
            employee.not_provided_transaction ||
            !employee.spouse_name ||
            !employee.spouse_status ||
            (employee.spouse_other && employee.spouse_other.includes('لم يزودنا'))
        );

        updateSearchResults();

    } catch (error) {
        console.error('خطأ في عرض الغير مزودين:', error);
        showMessage('خطأ', 'حدث خطأ في عرض الغير مزودين');
    }
}

// عرض جميع الموظفين
async function showAllEmployees() {
    if (!currentYear) {
        showMessage('تنبيه', 'يرجى اختيار السنة أولاً');
        return;
    }

    try {
        allEmployees = await ipcRenderer.invoke('get-employees-by-year', currentYear);
        filteredEmployees = allEmployees;
        updateSearchResults();

    } catch (error) {
        console.error('خطأ في تحميل الموظفين:', error);
        showMessage('خطأ', 'حدث خطأ في تحميل الموظفين');
    }
}

// تحديث نتائج البحث
function updateSearchResults() {
    const resultsCount = document.getElementById('resultsCount');
    const resultsTitle = document.getElementById('resultsTitle');

    resultsCount.textContent = `${filteredEmployees.length} نتيجة`;

    if (filteredEmployees.length === 0) {
        resultsTitle.textContent = 'لا توجد نتائج';
    } else {
        resultsTitle.textContent = 'نتائج البحث';
    }

    createSearchResultsTable(filteredEmployees);
}

// إنشاء جدول نتائج البحث
function createSearchResultsTable(employees) {
    const tableContainer = document.getElementById('searchResultsTable');

    const columns = [
        { key: 'name', title: 'اسم الموظف', width: '150px' },
        { key: 'spouse_name', title: 'اسم الزوج/الزوجة', width: '150px' },
        { key: 'spouse_status', title: 'حالة الزوج/الزوجة', width: '120px' },
        { key: 'children_count', title: 'عدد الأطفال', width: '100px' },
        {
            key: 'children_status',
            title: 'حالة الأطفال',
            width: '120px',
            render: (value, row) => {
                const allowedCount = row.children.filter(child => child.status === 'مسموح').length;
                const rejectedCount = row.children.filter(child => child.status === 'يرفع الابن').length;

                if (rejectedCount > 0) {
                    return `<span style="color: #dc3545; font-weight: bold;">${rejectedCount} مرفوض</span>`;
                } else if (allowedCount > 0) {
                    return `<span style="color: #28a745; font-weight: bold;">${allowedCount} مسموح</span>`;
                } else {
                    return '<span style="color: #6c757d;">لا يوجد</span>';
                }
            }
        },
        {
            key: 'status',
            title: 'الحالة العامة',
            width: '120px',
            render: (value, row) => {
                // فحص خانة "لم يزودنا بمعاملة" أولاً
                if (row.not_provided_transaction) {
                    return '<span style="color: #ffc107; font-weight: bold;">غير مزود</span>';
                }

                const hasRejectedChildren = row.children.some(child => child.status === 'يرفع الابن');
                const hasIncompleteData = !row.spouse_name || !row.spouse_status;
                const isNotProvidedOld = row.spouse_other && row.spouse_other.includes('لم يزودنا');

                if (isNotProvidedOld || hasRejectedChildren || hasIncompleteData) {
                    return '<span style="color: #dc3545; font-weight: bold;">ناقص</span>';
                } else {
                    return '<span style="color: #28a745; font-weight: bold;">مكتمل</span>';
                }
            }
        },
        {
            key: 'actions',
            title: 'الإجراءات',
            width: '120px',
            sortable: false,
            render: (value, row) => {
                return `
                    <div class="actions-column">
                        <button class="action-btn edit" onclick="editEmployee(${row.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteEmployee(${row.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            }
        }
    ];

    if (window.TableManager) {
        window.TableManager.createTable(employees, columns, 'searchResultsTable');
    } else {
        createSimpleTable(employees, columns, tableContainer);
    }
}

// تصدير نتائج البحث
async function exportSearchResults() {
    try {
        // إنشاء البيانات للتصدير
        const exportData = filteredEmployees.map((employee, index) => {
            const allowedChildren = employee.children.filter(child => child.status === 'مسموح').length;
            const rejectedChildren = employee.children.filter(child => child.status === 'يرفع الابن').length;

            let childrenStatus = 'لا يوجد';
            if (rejectedChildren > 0) {
                childrenStatus = `${rejectedChildren} مرفوض`;
            } else if (allowedChildren > 0) {
                childrenStatus = `${allowedChildren} مسموح`;
            }

            let generalStatus = 'مكتمل';

            // فحص خانة "لم يزودنا بمعاملة" أولاً
            if (employee.not_provided_transaction) {
                generalStatus = 'غير مزود';
            } else {
                const hasRejectedChildren = employee.children.some(child => child.status === 'يرفع الابن');
                const hasIncompleteData = !employee.spouse_name || !employee.spouse_status;
                const isNotProvidedOld = employee.spouse_other && employee.spouse_other.includes('لم يزودنا');

                if (isNotProvidedOld || hasRejectedChildren || hasIncompleteData) {
                    generalStatus = 'ناقص';
                }
            }

            return {
                'ت': index + 1,
                'اسم الموظف': employee.name || '',
                'اسم الزوج/الزوجة': employee.spouse_name || '',
                'حالة الزوج/الزوجة': employee.spouse_status || '',
                'مكان السكن': employee.spouse_residence || '',
                'عدم الاستفادة': employee.benefit_status || '',
                'لم يزود المعاملة': employee.not_provided_transaction ? 'نعم' : 'لا',
                'عدد الأطفال': employee.children_count || 0,
                'حالة الأطفال': childrenStatus,
                'الحالة العامة': generalStatus,
                'ملاحظات': employee.spouse_other || ''
            };
        });

        await exportToExcelFile(exportData, `نتائج_البحث_${currentYear}`);

    } catch (error) {
        console.error('خطأ في التصدير:', error);
        showMessage('خطأ', 'حدث خطأ في تصدير النتائج');
    }
}

// طباعة نتائج البحث
function printSearchResults() {
    try {
        printReportWithPreview('searchResultsTable', `نتائج البحث - السنة ${currentYear}`);
    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        showMessage('خطأ', 'حدث خطأ في طباعة النتائج');
    }
}

// دالة التأخير للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تصدير البيانات إلى ملف Excel حقيقي
async function exportToExcelFile(data, filename) {
    try {
        if (!data || data.length === 0) {
            showMessage('تنبيه', 'لا توجد بيانات للتصدير');
            return;
        }

        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

        // إنشاء worksheet من البيانات
        const ws = XLSX.utils.json_to_sheet(data);

        // تنسيق العرض للأعمدة
        const colWidths = [];
        const headers = Object.keys(data[0]);

        headers.forEach((header, index) => {
            let maxWidth = header.length;
            data.forEach(row => {
                const cellValue = String(row[header] || '');
                if (cellValue.length > maxWidth) {
                    maxWidth = cellValue.length;
                }
            });
            colWidths.push({ wch: Math.min(maxWidth + 2, 50) });
        });

        ws['!cols'] = colWidths;

        // إضافة تنسيق للرؤوس
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            if (!ws[cellAddress]) continue;

            ws[cellAddress].s = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "4472C4" } },
                alignment: { horizontal: "center", vertical: "center" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } }
                }
            };
        }

        // إضافة الـ worksheet إلى الـ workbook
        XLSX.utils.book_append_sheet(wb, ws, "تقرير الموظفين");

        // تصدير الملف
        XLSX.writeFile(wb, `${filename}.xlsx`);

        showMessage('نجح', `تم تصدير البيانات بنجاح إلى ملف ${filename}.xlsx`);

    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        showMessage('خطأ', 'حدث خطأ في تصدير البيانات');
    }
}

// طباعة التقرير مع معاينة
function printReportWithPreview(tableId, title) {
    try {
        const printWindow = window.open('', '_blank');
        const tableElement = document.getElementById(tableId);

        if (!tableElement) {
            showMessage('خطأ', 'لا توجد بيانات للطباعة');
            return;
        }

        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body {
                        font-family: 'Arial', sans-serif;
                        margin: 20px;
                        direction: rtl;
                        text-align: right;
                    }
                    .print-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                    }
                    .print-header h1 {
                        color: #333;
                        margin: 0;
                        font-size: 24px;
                    }
                    .print-header h2 {
                        color: #666;
                        margin: 10px 0;
                        font-size: 18px;
                    }
                    .print-info {
                        margin-bottom: 20px;
                        font-size: 14px;
                        color: #555;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                        font-size: 12px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background-color: #f5f5f5;
                        font-weight: bold;
                        color: #333;
                    }
                    tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }
                    .print-footer {
                        margin-top: 30px;
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <h1>نظام إدارة بيانات الموظفين</h1>
                    <h2>${title}</h2>
                </div>
                <div class="print-info">
                    <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>
                ${tableElement.outerHTML}
                <div class="print-footer">
                    <p>المبرمج: علي عاجل خشّان المحنّة | محافظة الديوانية / شعبة الرواتب</p>
                    <p>البريد الإلكتروني: <EMAIL> | الهاتف: 07727232639</p>
                </div>
                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">طباعة</button>
                    <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">إغلاق</button>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        showMessage('خطأ', 'حدث خطأ في طباعة التقرير');
    }
}
