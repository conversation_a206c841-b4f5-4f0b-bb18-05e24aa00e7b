===============================================
    نظام إدارة بيانات الموظفين
    المبرمج: علي عاجل خشّان المحنّة
===============================================

🚀 كيفية تشغيل البرنامج:

الطريقة الأولى (الأفضل والأسهل):
1. انقر نقراً مزدوجاً على ملف "تشغيل_نهائي.bat"
2. سيقوم بكل شيء تلقائياً (فحص + تثبيت + تشغيل)
3. ستفتح نافذة البرنامج تلقائياً

الطريقة الثانية (بسيطة وسريعة):
1. انقر نقراً مزدوجاً على ملف "RUN.bat"
2. تشغيل مباشر بدون تعقيدات
3. مناسب للاستخدام اليومي

الطريقة الثالثة (مع فحص شامل):
1. انقر نقراً مزدوجاً على ملف "تشغيل_تجريبي.bat"
2. فحص تفصيلي لجميع المتطلبات
3. مفيد لحل المشاكل

الطريقة الرابعة (يدوياً للمطورين):
1. افتح موجه الأوامر (Command Prompt)
2. انتقل لمجلد البرنامج
3. اكتب: npm install (للمرة الأولى فقط)
4. اكتب: npm start

===============================================

📋 دليل الاستخدام السريع:

1️⃣ شاشة البداية:
- ستظهر شاشة ترحيبية جميلة لمدة 5 ثوانٍ
- تحتوي على اسم البرنامج واسم المبرمج بألوان متحركة

2️⃣ اختيار السنة:
- أدخل السنة المطلوبة (مثل: 2025)
- أو اختر من السنوات المحفوظة
- اضغط "إضافة سنة جديدة" لحفظ سنة جديدة
- اضغط "دخول" للمتابعة

3️⃣ إدخال بيانات الموظف:
- اسم الموظف (مطلوب)
- اسم الزوج/الزوجة
- حالة الزوج/الزوجة (موظف/غير موظف/أخرى)
- عدد الأطفال (0-10)
- تفاصيل كل طفل:
  * الاسم
  * تاريخ الميلاد (يوم/شهر/سنة)
  * المرحلة الدراسية
  * الحالة (تظهر تلقائياً: مسموح/يرفع الابن)

4️⃣ التقارير:
- إحصائيات شاملة
- جدول بجميع البيانات
- إمكانية التصدير والطباعة

5️⃣ المقارنة السنوية:
- يظهر عند وجود أكثر من سنة
- مقارنة البيانات بين السنوات
- تصفيات متقدمة

6️⃣ الإعدادات:
- تغيير الثيم (7 خيارات ملونة)
- إدارة قاعدة البيانات
- نبذة عن المبرمج

===============================================

🎨 الثيمات المتاحة:

1. البركاني (أحمر داكن وأسود)
2. البنفسجي والأزرق
3. الفضي والأصفر
4. السمائي
5. الرصاصي والسماوي
6. الأخضر الحشيشي
7. البرتقالي

===============================================

⚠️ قواعد أعمار الأطفال:

✅ مسموح:
- عمر 18 سنة أو أقل (أي مرحلة)
- 18 سنة في الإعدادية
- أقل من 24 سنة في الجامعة

❌ يرفع الابن:
- أكبر من 18 سنة في متوسطة أو إعدادية
- 20 سنة أو أكثر في متوسطة أو إعدادية
- 24 سنة أو أكثر في الجامعة

===============================================

💾 إدارة البيانات:

📁 مكان حفظ البيانات:
- مجلد "database_safe" في نفس مكان البرنامج
- ملف "employees.db" يحتوي على جميع البيانات

🔄 النسخ الاحتياطي:
- من تبويب الإعدادات
- اختر "تصدير البيانات"
- احفظ الملف في مكان آمن

🗑️ تفريغ القاعدة:
- من تبويب الإعدادات
- اختر "تفريغ القاعدة"
- تأكيد العملية (لا يمكن التراجع)

===============================================

🔧 حل المشاكل الشائعة:

❓ البرنامج لا يعمل:
- تأكد من تثبيت Node.js
- شغل "start.bat" كمدير
- تأكد من وجود جميع الملفات

❓ خطأ في قاعدة البيانات:
- تأكد من وجود مجلد "database_safe"
- تأكد من صلاحيات الكتابة
- أعد تشغيل البرنامج

❓ البيانات لا تظهر:
- تأكد من اختيار السنة الصحيحة
- تأكد من حفظ البيانات بنجاح
- جرب تحديث الصفحة (F5)

===============================================

📞 الدعم الفني:

المبرمج: علي عاجل خشّان المحنّة
البريد الإلكتروني: <EMAIL>
الهاتف/واتساب: 07727232639

===============================================

© 2025 - علي عاجل خشّان المحنّة
جميع الحقوق محفوظة

نشكرك لاستخدام نظام إدارة بيانات الموظفين!
===============================================
