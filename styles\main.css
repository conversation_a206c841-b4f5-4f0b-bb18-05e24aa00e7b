/* الخطوط والإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
}

.hidden {
    display: none !important;
}

/* شاشة البداية */
.splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1e3c72, #2a5298, #667eea, #764ba2);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.splash-content {
    text-align: center;
    color: white;
}

.animated-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.programmer-name {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 3rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
    background-size: 600% 600%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbowText 3s ease-in-out infinite;
}

.animated-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    animation: pulse 2s infinite;
}

.loading-bar {
    width: 300px;
    height: 6px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
    border-radius: 3px;
    animation: loading 3s ease-in-out;
}

/* التطبيق الرئيسي */
.main-app {
    min-height: 100vh;
    background: var(--bg-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
}

.app-header {
    background: var(--header-bg, rgba(255,255,255,0.1));
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.programmer-signature {
    font-size: 1.1rem;
    font-weight: 600;
}

.glowing-text {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #feca57);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glowingColors 2s ease-in-out infinite;
    text-shadow: 0 0 20px rgba(255,255,255,0.5);
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

.app-logo i {
    font-size: 1.5rem;
}

/* اختيار السنة */
.year-selection-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 80px);
    padding: 2rem;
}

.year-selection-card {
    background: var(--card-bg, rgba(255,255,255,0.95));
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 500px;
    width: 100%;
    text-align: center;
}

.section-title {
    color: var(--primary-color, #667eea);
    font-size: 2rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.year-input-group {
    margin-bottom: 2rem;
}

.year-input-group label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-color, #333);
    font-size: 1.1rem;
}

.input-with-dropdown {
    position: relative;
    display: flex;
    gap: 0.5rem;
}

#yearInput {
    flex: 1;
    padding: 1rem;
    border: 2px solid var(--border-color, #ddd);
    border-radius: 10px;
    font-size: 1.1rem;
    text-align: center;
    transition: all 0.3s ease;
}

#yearInput:focus {
    outline: none;
    border-color: var(--primary-color, #667eea);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.dropdown-btn {
    padding: 1rem;
    background: var(--primary-color, #667eea);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropdown-btn:hover {
    background: var(--primary-hover, #5a67d8);
    transform: translateY(-2px);
}

.year-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 1000;
    margin-top: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-header {
    padding: 1rem;
    background: var(--primary-color, #667eea);
    color: white;
    font-weight: 600;
    border-radius: 10px 10px 0 0;
}

.years-list {
    padding: 0.5rem 0;
}

.year-item {
    padding: 0.8rem 1rem;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.year-item:hover {
    background: var(--hover-bg, #f8f9ff);
}

.year-item:last-child {
    border-bottom: none;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color, #667eea);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover, #5a67d8);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: var(--secondary-color, #6c757d);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-hover, #5a6268);
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color, #28a745);
    color: white;
}

.btn-success:hover {
    background: var(--success-hover, #218838);
    transform: translateY(-2px);
}

.btn-warning {
    background: var(--warning-color, #ffc107);
    color: #212529;
}

.btn-warning:hover {
    background: var(--warning-hover, #e0a800);
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger-color, #dc3545);
    color: white;
}

.btn-danger:hover {
    background: var(--danger-hover, #c82333);
    transform: translateY(-2px);
}

.btn-info {
    background: var(--info-color, #17a2b8);
    color: white;
}

.btn-info:hover {
    background: var(--info-hover, #138496);
    transform: translateY(-2px);
}

/* واجهة التبويبات */
.tabs-interface {
    min-height: 100vh;
    background: var(--bg-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
}

.tabs-nav {
    background: var(--nav-bg, rgba(255,255,255,0.1));
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    gap: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    align-items: center;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: transparent;
    color: rgba(255,255,255,0.8);
    border: 2px solid transparent;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.tab-btn.active {
    background: var(--primary-color, #667eea);
    color: white;
    border-color: var(--primary-color, #667eea);
}

.back-btn {
    margin-right: auto;
    background: var(--danger-color, #dc3545);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.back-btn:hover {
    background: var(--danger-hover, #c82333);
    transform: translateY(-2px);
}

.tabs-content {
    padding: 2rem;
}

.tab-content {
    display: none;
    background: var(--content-bg, rgba(255,255,255,0.95));
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color, #eee);
}

.content-header h2 {
    color: var(--primary-color, #667eea);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.8rem;
}

.current-year {
    background: var(--primary-color, #667eea);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

/* النماذج */
.employee-form {
    max-width: 800px;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--section-bg, #f8f9ff);
    border-radius: 15px;
    border: 1px solid var(--border-color, #eee);
}

.form-section h3 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color, #333);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color, #ddd);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color, #667eea);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-group {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.other-input {
    margin-top: 1rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-color, #eee);
}

/* الإحصائيات */
.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg, white);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--primary-color, #667eea);
}

.stat-info h3 {
    font-size: 1rem;
    color: var(--text-color, #666);
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color, #667eea);
}

/* الجداول */
.data-table-container {
    margin-top: 2rem;
    overflow-x: auto;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 15px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: var(--primary-color, #667eea);
    color: white;
    font-weight: 600;
}

.data-table tr:hover {
    background: var(--hover-bg, #f8f9ff);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1rem;
}

.modal-content p {
    margin-bottom: 2rem;
    color: var(--text-color, #666);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* تفاصيل الأطفال */
.children-details {
    margin-top: 1rem;
}

.child-section {
    background: var(--card-bg, white);
    border: 2px solid var(--border-color, #eee);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.child-section:hover {
    border-color: var(--primary-color, #667eea);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.child-section h4 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.child-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.date-inputs {
    display: flex;
    gap: 0.5rem;
}

.date-inputs input {
    flex: 1;
    text-align: center;
}

.status-field {
    font-weight: 600;
    text-align: center;
}

.status-allowed {
    background-color: #d4edda !important;
    color: #155724 !important;
    border-color: #c3e6cb !important;
}

.status-rejected {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    border-color: #f5c6cb !important;
}

.warning-message {
    background: #fff3cd;
    color: #856404;
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid #ffeaa7;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* تحسينات التقرير */
.report-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.comparison-filters {
    margin-bottom: 2rem;
}

.comparison-filters h3 {
    color: var(--primary-color, #667eea);
    margin-bottom: 1rem;
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background: var(--secondary-color, #6c757d);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover {
    background: var(--primary-color, #667eea);
    transform: translateY(-2px);
}

.filter-btn.active {
    background: var(--primary-color, #667eea);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* تحسينات الجداول */
.data-table {
    font-size: 0.9rem;
}

.data-table th {
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:nth-child(even) {
    background-color: var(--hover-bg, #f8f9ff);
}

.data-table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.data-table .actions-column {
    white-space: nowrap;
    width: 120px;
}

.action-btn {
    padding: 0.3rem 0.6rem;
    margin: 0 0.2rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background: var(--warning-color, #ffc107);
    color: #212529;
}

.action-btn.delete {
    background: var(--danger-color, #dc3545);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* الاستجابة */
@media (max-width: 768px) {
    .tabs-nav {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .tab-btn {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }

    .statistics-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .checkbox-group {
        flex-direction: column;
        gap: 1rem;
    }

    .child-fields {
        grid-template-columns: 1fr;
    }

    .date-inputs {
        flex-direction: column;
    }

    .report-actions,
    .filter-buttons {
        flex-direction: column;
    }

    .data-table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }
}
