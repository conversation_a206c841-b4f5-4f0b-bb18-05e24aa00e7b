🎯 تعليمات تشغيل نظام إدارة بيانات الموظفين 🎯

المبرمج: علي عاجل خشّان المحنّة
محافظة الديوانية / شعبة الرواتب

================================================

📋 ملفات التشغيل المتاحة:

1️⃣ LAUNCH.bat - الملف الرئيسي للتشغيل
2️⃣ start.bat - ملف تشغيل بديل

================================================

🚀 كيفية تشغيل البرنامج:

الطريقة الأولى (الموصى بها):
• انقر نقراً مزدوجاً على ملف "LAUNCH.bat"
• سيظهر نافذة سوداء لثوانٍ قليلة ثم تختفي
• سيفتح البرنامج تلقائياً

الطريقة الثانية:
• انقر نقراً مزدوجاً على ملف "start.bat"
• نفس النتيجة

================================================

⚠️ متطلبات التشغيل:

✅ Node.js مثبت على النظام
   - إذا لم يكن مثبت، حمله من: https://nodejs.org

✅ اتصال بالإنترنت (للتشغيل الأول فقط)
   - لتحميل التبعيات المطلوبة

================================================

🔧 حل المشاكل الشائعة:

❌ إذا ظهر خطأ "Node.js غير مثبت":
   → ثبت Node.js من الموقع الرسمي

❌ إذا ظهر خطأ "فشل في تثبيت التبعيات":
   → تأكد من اتصالك بالإنترنت
   → حاول مرة أخرى

❌ إذا لم يفتح البرنامج:
   → تأكد من وجود جميع ملفات البرنامج
   → جرب تشغيل الملف كمدير

================================================

📞 للدعم الفني:

📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف/واتساب: 07727232639

================================================

✨ ملاحظات مهمة:

• النافذة السوداء ستختفي تلقائياً
• لا تحتاج لأي إعدادات إضافية
• البرنامج يعمل بدون إنترنت بعد التشغيل الأول
• جميع البيانات محفوظة محلياً على جهازك

================================================

🎉 استمتع باستخدام البرنامج! 🎉
