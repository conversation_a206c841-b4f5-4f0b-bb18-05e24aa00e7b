@echo off
chcp 65001 >nul
title 🚀 نظام إدارة بيانات الموظفين - المبرمج: علي عاجل خشّان المحنّة

REM الانتقال إلى مجلد البرنامج
cd /d "%~dp0"

REM تعيين ألوان
color 0B

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    🏢 نظام إدارة بيانات الموظفين 🏢                       ║
echo ║                                                                              ║
echo ║                  👨‍💻 المبرمج: علي عاجل خشّان المحنّة 👨‍💻                ║
echo ║                                                                              ║
echo ║                  🏛️ محافظة الديوانية / شعبة الرواتب 🏛️                 ║
echo ║                                                                              ║
echo ║                📧 <EMAIL>  📱 07727232639                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص وتحضير المتطلبات...
echo.

REM فحص Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت على هذا الكمبيوتر
    echo 📥 جاري تحميل وتثبيت Node.js تلقائياً...
    echo ⏳ يرجى الانتظار (قد يستغرق 3-5 دقائق)...
    echo.
    
    REM تحديد معمارية النظام
    if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
        set NODE_URL=https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi
    ) else (
        set NODE_URL=https://nodejs.org/dist/v20.11.0/node-v20.11.0-x86.msi
    )
    
    REM تحميل Node.js باستخدام PowerShell
    powershell -Command "try { Invoke-WebRequest -Uri '%NODE_URL%' -OutFile 'nodejs-installer.msi' -UseBasicParsing; Write-Host '✅ تم التحميل بنجاح' } catch { Write-Host '❌ فشل التحميل'; exit 1 }"
    
    if exist "nodejs-installer.msi" (
        echo 🔧 تثبيت Node.js...
        echo ⚠️ قد تظهر نافذة تأكيد - اضغط "نعم" للمتابعة
        
        REM تثبيت Node.js
        start /wait msiexec /i "nodejs-installer.msi" /quiet /norestart
        
        REM حذف ملف التثبيت
        del "nodejs-installer.msi" >nul 2>nul
        
        REM إعادة تحديث متغيرات البيئة
        for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%b"
        for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
        set "PATH=%SystemPath%;%UserPath%"
        
        REM التحقق من نجاح التثبيت
        where node >nul 2>nul
        if %ERRORLEVEL% EQU 0 (
            echo ✅ تم تثبيت Node.js بنجاح!
        ) else (
            echo ⚠️ تم التثبيت - يرجى إعادة تشغيل الكمبيوتر أو فتح موجه أوامر جديد
            echo 💡 أو قم بتشغيل هذا الملف مرة أخرى
            pause
            exit /b 0
        )
    ) else (
        echo ❌ فشل في تحميل Node.js
        echo 🌐 تأكد من اتصالك بالإنترنت
        echo 📞 أو قم بتثبيت Node.js يدوياً من: https://nodejs.org
        echo 📞 للدعم: <EMAIL> أو 07727232639
        pause
        exit /b 1
    )
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
    echo ✅ Node.js موجود - الإصدار: %NODE_VERSION%
)

REM فحص ملفات المشروع
if not exist "package.json" (
    color 0C
    echo ❌ ملفات المشروع غير موجودة!
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo 📞 للدعم: <EMAIL> أو 07727232639
    pause
    exit /b 1
)
echo ✅ ملفات المشروع موجودة

REM إنشاء مجلد قاعدة البيانات
if not exist "database_safe" (
    mkdir "database_safe" >nul 2>nul
    echo ✅ تم إنشاء مجلد قاعدة البيانات
)

REM فحص وتثبيت التبعيات
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات المطلوبة للمرة الأولى...
    echo ⏳ يرجى الانتظار (قد يستغرق 2-5 دقائق)...
    echo 🌐 تأكد من اتصالك بالإنترنت
    echo.
    
    REM تثبيت التبعيات مع إظهار التقدم
    npm install --progress=true --no-audit --no-fund
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ❌ فشل في تثبيت التبعيات!
        echo 🔧 محاولة إصلاح المشكلة...
        
        REM تنظيف وإعادة المحاولة
        npm cache clean --force >nul 2>nul
        if exist "node_modules" rmdir /s /q "node_modules" >nul 2>nul
        if exist "package-lock.json" del "package-lock.json" >nul 2>nul
        
        echo 🔄 إعادة المحاولة مع إعدادات مختلفة...
        npm install --no-optional --legacy-peer-deps --no-audit --no-fund
        
        if %ERRORLEVEL% NEQ 0 (
            color 0C
            echo ❌ فشل في تثبيت التبعيات نهائياً!
            echo 🌐 تأكد من اتصالك بالإنترنت
            echo 🔧 جرب تشغيل موجه الأوامر كمسؤول
            echo 📞 للدعم الفني: <EMAIL> أو 07727232639
            pause
            exit /b 1
        )
    )
    
    echo ✅ تم تثبيت جميع التبعيات بنجاح!
) else (
    echo ✅ التبعيات موجودة ومثبتة
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           🚀 تشغيل البرنامج 🚀                            ║
echo ║                                                                              ║
echo ║                    💡 ستفتح نافذة البرنامج خلال ثوانٍ                     ║
echo ║                                                                              ║
echo ║                  🔄 لإغلاق البرنامج: أغلق النافذة أو Ctrl+C               ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM تشغيل البرنامج
npm start

REM في حالة إغلاق البرنامج
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              📴 تم إغلاق البرنامج                         ║
echo ║                                                                              ║
echo ║                    🙏 شكراً لاستخدام نظام إدارة البيانات                  ║
echo ║                                                                              ║
echo ║                          📞 للدعم الفني:                                  ║
echo ║                    📧 <EMAIL>                                   ║
echo ║                    📱 07727232639                                          ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

timeout /t 5 /nobreak >nul